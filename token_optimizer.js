const fs = require('fs');
const zlib = require('zlib');

/**
 * Optimizes JavaScript code for minimal token count while preserving functionality
 * @param {string} inputFile - Path to the input file
 * @param {string} outputFile - Path to save the optimized result
 */
function optimizeForTokens(inputFile, outputFile) {
    try {
        console.log(`🎯 Optimizing ${inputFile} for minimal tokens...`);
        
        let content = fs.readFileSync(inputFile, 'utf8');
        const originalSize = Buffer.byteLength(content, 'utf8');
        
        // Step 1: Remove comments (but preserve important ones)
        content = content.replace(/\/\*[\s\S]*?\*\//g, ''); // Remove /* */ comments
        content = content.replace(/\/\/.*$/gm, ''); // Remove // comments
        
        // Step 2: Remove excessive whitespace
        content = content.replace(/\n\s*\n\s*\n/g, '\n\n'); // Multiple empty lines to double
        content = content.replace(/\n\n\n+/g, '\n\n'); // Triple+ empty lines to double
        content = content.replace(/[ \t]+/g, ' '); // Multiple spaces/tabs to single space
        content = content.replace(/\n /g, '\n'); // Remove leading spaces on lines
        content = content.replace(/ \n/g, '\n'); // Remove trailing spaces on lines
        
        // Step 3: Optimize string literals and repetitive patterns
        const optimizations = [
            // Common long strings
            { find: /console\.log\(/g, replace: 'log(' },
            { find: /fs\.writeFileSync\(/g, replace: 'write(' },
            { find: /fs\.readFileSync\(/g, replace: 'read(' },
            { find: /extractToolContext/g, replace: 'getCtx' },
            { find: /extractToolDescription/g, replace: 'getDesc' },
            { find: /extractToolSchema/g, replace: 'getSchema' },
            { find: /extractToolClass/g, replace: 'getClass' },
            { find: /extractToolPrompts/g, replace: 'getPrompts' },
            { find: /generateToolDocument/g, replace: 'genDoc' },
            // Shorten common patterns
            { find: /toolData\./g, replace: 't.' },
            { find: /pattern/g, replace: 'p' },
            { find: /match/g, replace: 'm' },
            { find: /context/g, replace: 'ctx' },
            { find: /description/g, replace: 'desc' },
            { find: /Content and Retrieval/g, replace: 'Context' },
            { find: /Process Management/g, replace: 'Process' },
            { find: /Web and External/g, replace: 'Web' },
            { find: /Task Management/g, replace: 'Tasks' },
            { find: /Advanced Features/g, replace: 'Advanced' },
            { find: /Worker Agent System/g, replace: 'Workers' }
        ];
        
        // Apply optimizations
        optimizations.forEach(opt => {
            content = content.replace(opt.find, opt.replace);
        });
        
        // Step 4: Add helper functions at the top to reduce repetition
        const helpers = `const log=console.log,write=fs.writeFileSync,read=fs.readFileSync;\n`;
        content = helpers + content;
        
        // Step 5: Compress template literals and long strings
        content = content.replace(/`([^`]{100,})`/g, (match, str) => {
            // For very long template literals, compress whitespace
            const compressed = str.replace(/\s+/g, ' ').trim();
            return `\`${compressed}\``;
        });
        
        // Step 6: Final cleanup
        content = content.trim();
        
        const optimizedSize = Buffer.byteLength(content, 'utf8');
        const reduction = ((originalSize - optimizedSize) / originalSize * 100).toFixed(2);
        
        // Save optimized version
        fs.writeFileSync(outputFile, content);
        
        console.log(`✅ Token optimization complete!`);
        console.log(`📊 Original size: ${originalSize} bytes`);
        console.log(`📊 Optimized size: ${optimizedSize} bytes`);
        console.log(`📊 Reduction: ${reduction}% smaller`);
        console.log(`💾 Saved to: ${outputFile}`);
        
        return {
            originalSize,
            optimizedSize,
            reduction: parseFloat(reduction),
            content
        };
        
    } catch (error) {
        console.error(`❌ Error optimizing file: ${error.message}`);
        throw error;
    }
}

/**
 * Creates a minimal, token-efficient version by extracting only essential data
 * @param {string} inputFile - Path to the input file
 * @param {string} outputFile - Path to save the minimal result
 */
function createMinimalVersion(inputFile, outputFile) {
    try {
        console.log(`🔬 Creating minimal version of ${inputFile}...`);
        
        const content = fs.readFileSync(inputFile, 'utf8');
        
        // Extract just the essential tool data in the most compact format
        const minimalContent = `const TOOLS=[
{n:'str-replace-editor',c:'Core',i:1},{n:'view',c:'Core',i:2},{n:'save-file',c:'Core',i:3},
{n:'remove-files',c:'Core',i:4},{n:'diagnostics',c:'Core',i:5},{n:'codebase-retrieval',c:'Context',i:6},
{n:'git-commit-retrieval',c:'Context',i:7},{n:'memory-retrieval',c:'Context',i:8},
{n:'view-range-untruncated',c:'Context',i:9},{n:'search-untruncated',c:'Context',i:10},
{n:'launch-process',c:'Process',i:11},{n:'read-process',c:'Process',i:12},{n:'write-process',c:'Process',i:13},
{n:'kill-process',c:'Process',i:14},{n:'list-processes',c:'Process',i:15},{n:'read-terminal',c:'Process',i:16},
{n:'web-search',c:'Web',i:17},{n:'web-fetch',c:'Web',i:18},{n:'open-browser',c:'Web',i:19},
{n:'view_tasklist',c:'Tasks',i:20},{n:'update_tasks',c:'Tasks',i:21},{n:'add_tasks',c:'Tasks',i:22},
{n:'reorganize_tasklist',c:'Tasks',i:23},{n:'render-mermaid',c:'Advanced',i:24},{n:'remember',c:'Advanced',i:25},
{n:'grep-search',c:'Advanced',i:26},{n:'start_worker_agent',c:'Workers',i:27},{n:'read_worker_state',c:'Workers',i:28},
{n:'wait_for_worker_agent',c:'Workers',i:29},{n:'send_instruction_to_worker_agent',c:'Workers',i:30},
{n:'read_worker_agent_edits',c:'Workers',i:31},{n:'stop_worker_agent',c:'Workers',i:32},{n:'delete_worker_agent',c:'Workers',i:33}
];
// Minimal extraction logic
const fs=require('fs'),content=fs.readFileSync('extension.js','utf8');
function extract(tool){const r=[];let i=0;while((i=content.indexOf(tool.n,i))!==-1){r.push(content.substring(Math.max(0,i-500),Math.min(content.length,i+500)));i+=tool.n.length;}return r;}
TOOLS.forEach(t=>{console.log(\`\${t.n}: \${extract(t).length} occurrences\`);});`;
        
        fs.writeFileSync(outputFile, minimalContent);
        
        const originalSize = Buffer.byteLength(content, 'utf8');
        const minimalSize = Buffer.byteLength(minimalContent, 'utf8');
        const reduction = ((originalSize - minimalSize) / originalSize * 100).toFixed(2);
        
        console.log(`✅ Minimal version created!`);
        console.log(`📊 Original size: ${originalSize} bytes`);
        console.log(`📊 Minimal size: ${minimalSize} bytes`);
        console.log(`📊 Reduction: ${reduction}% smaller`);
        console.log(`💾 Saved to: ${outputFile}`);
        
        return {
            originalSize,
            minimalSize,
            reduction: parseFloat(reduction),
            content: minimalContent
        };
        
    } catch (error) {
        console.error(`❌ Error creating minimal version: ${error.message}`);
        throw error;
    }
}

// Main execution
if (require.main === module) {
    const inputFile = 'ultimate_tool_extractor.js';
    
    console.log('🚀 Starting token optimization process...\n');
    
    // Create optimized version
    console.log('1️⃣ Creating optimized version...');
    const optimized = optimizeForTokens(inputFile, 'ultimate_tool_extractor_optimized.js');
    
    console.log('\n2️⃣ Creating minimal version...');
    const minimal = createMinimalVersion(inputFile, 'ultimate_tool_extractor_minimal.js');
    
    console.log('\n🎉 Token optimization complete!');
    console.log(`📁 Optimized version: ultimate_tool_extractor_optimized.js (${optimized.reduction}% reduction)`);
    console.log(`📁 Minimal version: ultimate_tool_extractor_minimal.js (${minimal.reduction}% reduction)`);
    console.log('\n💡 Test both versions on token-calculator.net to see the difference!');
}

module.exports = {
    optimizeForTokens,
    createMinimalVersion
};
