const fs = require('fs');
const { spawn, exec } = require('child_process');
const path = require('path');

/**
 * Comprehensive Code Compressor - Uses multiple proven strategies
 * Implements techniques from: Repomix, Terser, UglifyJS, and custom optimizations
 */
class ComprehensiveCompressor {
    constructor() {
        this.availableTools = {
            terser: { installed: false, command: 'terser' },
            repomix: { installed: false, command: 'repomix' },
            uglifyjs: { installed: false, command: 'uglifyjs' }
        };
        
        this.strategies = [
            { name: 'terser', priority: 1, description: 'Industry-standard JS minifier' },
            { name: 'repomix', priority: 2, description: 'AI-optimized code compression (~70% reduction)' },
            { name: 'custom', priority: 3, description: 'Custom token-optimized compression' }
        ];
    }

    /**
     * Check which compression tools are available
     */
    async checkAvailableTools() {
        console.log('🔍 Checking available compression tools...\n');
        
        for (const [tool, config] of Object.entries(this.availableTools)) {
            try {
                await this.runCommand(`${config.command} --version`);
                config.installed = true;
                console.log(`✅ ${tool} - Available`);
            } catch (error) {
                config.installed = false;
                console.log(`❌ ${tool} - Not installed`);
            }
        }
        
        console.log('\n📦 Installing missing tools...\n');
        await this.installMissingTools();
    }

    /**
     * Install missing compression tools
     */
    async installMissingTools() {
        const installCommands = {
            terser: 'npm install -g terser',
            repomix: 'npm install -g repomix',
            uglifyjs: 'npm install -g uglify-js'
        };

        for (const [tool, config] of Object.entries(this.availableTools)) {
            if (!config.installed) {
                console.log(`📥 Installing ${tool}...`);
                try {
                    await this.runCommand(installCommands[tool]);
                    config.installed = true;
                    console.log(`✅ ${tool} installed successfully`);
                } catch (error) {
                    console.log(`⚠️  Failed to install ${tool}: ${error.message}`);
                }
            }
        }
    }

    /**
     * Run shell command as promise
     */
    runCommand(command) {
        return new Promise((resolve, reject) => {
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(stdout);
                }
            });
        });
    }

    /**
     * Strategy 1: Use Terser for professional-grade minification
     */
    async compressWithTerser(inputFile, outputFile) {
        if (!this.availableTools.terser.installed) {
            throw new Error('Terser not available');
        }

        console.log('🗜️  Compressing with Terser...');
        
        const terserOptions = [
            inputFile,
            '-o', outputFile,
            '--compress', 'sequences=true,dead_code=true,conditionals=true,booleans=true,unused=true,if_return=true,join_vars=true,passes=2',
            '--mangle', 'toplevel=true',
            '--format', 'semicolons=false,beautify=false'
        ];

        try {
            await this.runCommand(`terser ${terserOptions.join(' ')}`);
            return this.getCompressionStats(inputFile, outputFile, 'Terser');
        } catch (error) {
            throw new Error(`Terser compression failed: ${error.message}`);
        }
    }

    /**
     * Strategy 2: Use Repomix for AI-optimized compression
     */
    async compressWithRepomix(inputFile, outputFile) {
        if (!this.availableTools.repomix.installed) {
            throw new Error('Repomix not available');
        }

        console.log('🤖 Compressing with Repomix (AI-optimized)...');
        
        try {
            // Repomix with compression enabled
            await this.runCommand(`repomix ${inputFile} --compress --output ${outputFile}`);
            return this.getCompressionStats(inputFile, outputFile, 'Repomix');
        } catch (error) {
            throw new Error(`Repomix compression failed: ${error.message}`);
        }
    }

    /**
     * Strategy 3: Custom token-optimized compression
     */
    async compressWithCustom(inputFile, outputFile) {
        console.log('🎯 Applying custom token optimization...');
        
        let code = fs.readFileSync(inputFile, 'utf8');
        const originalSize = Buffer.byteLength(code, 'utf8');
        
        // Apply systematic optimizations
        code = this.applyTokenOptimizations(code);
        code = this.applyStructuralOptimizations(code);
        code = this.applySemanticOptimizations(code);
        
        fs.writeFileSync(outputFile, code);
        
        return this.getCompressionStats(inputFile, outputFile, 'Custom');
    }

    /**
     * Apply token-level optimizations
     */
    applyTokenOptimizations(code) {
        // Property name compression
        const propertyMappings = {
            'name': 'n', 'category': 'c', 'id': 'i', 'description': 'd',
            'type': 't', 'value': 'v', 'index': 'x', 'length': 'l',
            'content': 'cnt', 'context': 'ctx', 'pattern': 'p', 'match': 'm'
        };

        Object.entries(propertyMappings).forEach(([long, short]) => {
            // Replace object property definitions
            code = code.replace(new RegExp(`\\b${long}:\\s*`, 'g'), `${short}:`);
            // Replace property access
            code = code.replace(new RegExp(`\\.${long}\\b`, 'g'), `.${short}`);
        });

        // Category value compression
        const categoryMappings = {
            'Core Development': 'Core',
            'Context and Retrieval': 'Context',
            'Process Management': 'Process',
            'Web and External': 'Web',
            'Task Management': 'Tasks',
            'Advanced Features': 'Advanced',
            'Worker Agent System': 'Workers'
        };

        Object.entries(categoryMappings).forEach(([long, short]) => {
            code = code.replace(new RegExp(`'${long}'`, 'g'), `'${short}'`);
            code = code.replace(new RegExp(`"${long}"`, 'g'), `"${short}"`);
        });

        return code;
    }

    /**
     * Apply structural optimizations
     */
    applyStructuralOptimizations(code) {
        // Remove comments
        code = code.replace(/\/\*[\s\S]*?\*\//g, '');
        code = code.replace(/\/\/.*$/gm, '');
        
        // Compress whitespace
        code = code.replace(/\s+/g, ' ');
        code = code.replace(/\s*([{}();,])\s*/g, '$1');
        
        // Remove unnecessary semicolons
        code = code.replace(/;+/g, ';');
        code = code.replace(/;\s*}/g, '}');
        
        return code.trim();
    }

    /**
     * Apply semantic optimizations
     */
    applySemanticOptimizations(code) {
        // Compress function declarations
        code = code.replace(/function\s+(\w+)\s*\(/g, 'function $1(');
        
        // Compress object literals
        code = code.replace(/\{\s*([^}]+)\s*\}/g, (_, content) => {
            const compressed = content.replace(/\s*,\s*/g, ',').replace(/\s*:\s*/g, ':');
            return `{${compressed}}`;
        });
        
        // Compress array literals
        code = code.replace(/\[\s*([^\]]+)\s*\]/g, (_, content) => {
            const compressed = content.replace(/\s*,\s*/g, ',');
            return `[${compressed}]`;
        });
        
        return code;
    }

    /**
     * Get compression statistics
     */
    getCompressionStats(inputFile, outputFile, method) {
        const originalSize = fs.statSync(inputFile).size;
        const compressedSize = fs.statSync(outputFile).size;
        const reduction = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);
        
        return {
            method,
            originalSize,
            compressedSize,
            reduction: parseFloat(reduction),
            ratio: `${reduction}%`
        };
    }

    /**
     * Main compression method - tries all available strategies
     */
    async compressFile(inputFile, outputDir = './compressed') {
        console.log(`🚀 Starting comprehensive compression of ${inputFile}...\n`);
        
        // Ensure output directory exists
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        
        // Check available tools
        await this.checkAvailableTools();
        
        const results = [];
        const baseName = path.basename(inputFile, path.extname(inputFile));
        
        // Try each compression strategy
        for (const strategy of this.strategies) {
            const outputFile = path.join(outputDir, `${baseName}_${strategy.name}${path.extname(inputFile)}`);
            
            try {
                let result;
                switch (strategy.name) {
                    case 'terser':
                        if (this.availableTools.terser.installed) {
                            result = await this.compressWithTerser(inputFile, outputFile);
                        }
                        break;
                    case 'repomix':
                        if (this.availableTools.repomix.installed) {
                            result = await this.compressWithRepomix(inputFile, outputFile);
                        }
                        break;
                    case 'custom':
                        result = await this.compressWithCustom(inputFile, outputFile);
                        break;
                }
                
                if (result) {
                    result.description = strategy.description;
                    result.outputFile = outputFile;
                    results.push(result);
                    console.log(`✅ ${result.method}: ${result.ratio} reduction (${result.compressedSize} bytes)`);
                }
            } catch (error) {
                console.log(`❌ ${strategy.name} failed: ${error.message}`);
            }
        }
        
        // Display final results
        this.displayResults(results);
        
        return results;
    }

    /**
     * Display compression results
     */
    displayResults(results) {
        if (results.length === 0) {
            console.log('\n❌ No compression methods succeeded');
            return;
        }
        
        console.log('\n🎉 Compression Results Summary:');
        console.log('═'.repeat(60));
        
        // Sort by reduction percentage
        results.sort((a, b) => b.reduction - a.reduction);
        
        results.forEach((result, index) => {
            const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📊';
            console.log(`${medal} ${result.method}: ${result.ratio} reduction`);
            console.log(`   📁 ${result.outputFile}`);
            console.log(`   📊 ${result.originalSize} → ${result.compressedSize} bytes`);
            console.log(`   💡 ${result.description}`);
            console.log('');
        });
        
        const best = results[0];
        console.log(`🏆 Best result: ${best.method} with ${best.ratio} reduction`);
        console.log(`💡 Recommendation: Use ${best.outputFile} for maximum token savings`);
    }
}

// Main execution
if (require.main === module) {
    const compressor = new ComprehensiveCompressor();
    
    const inputFile = process.argv[2] || 'ultimate_tool_extractor.js';
    
    if (!fs.existsSync(inputFile)) {
        console.error(`❌ Input file ${inputFile} not found`);
        process.exit(1);
    }
    
    compressor.compressFile(inputFile)
        .then(results => {
            console.log('\n🎯 Compression complete! Test the results on token-calculator.net');
        })
        .catch(error => {
            console.error(`❌ Compression failed: ${error.message}`);
            process.exit(1);
        });
}

module.exports = ComprehensiveCompressor;
