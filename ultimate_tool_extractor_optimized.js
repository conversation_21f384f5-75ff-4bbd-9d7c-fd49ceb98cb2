const log=console.log,write=fs.writeFileSync,read=fs.readFileSync;
const fs = require('fs');
const path = require('path');

const content = read('extension.js', 'utf8');

log('🔥 ULTIMATE TOOL EXTRACTION - MAKING HISTORY! 🔥\n');

const TOOLS = [

{ name: 'str-replace-editor', category: 'Core Development', id: 1 },
{ name: 'view', category: 'Core Development', id: 2 },
{ name: 'save-file', category: 'Core Development', id: 3 },
{ name: 'remove-files', category: 'Core Development', id: 4 },
{ name: 'diagnostics', category: 'Core Development', id: 5 },

{ name: 'codebase-retrieval', category: 'Context and Retrieval', id: 6 },
{ name: 'git-commit-retrieval', category: 'Context and Retrieval', id: 7 },
{ name: 'memory-retrieval', category: 'Context and Retrieval', id: 8 },
{ name: 'view-range-untruncated', category: 'Context and Retrieval', id: 9 },
{ name: 'search-untruncated', category: 'Context and Retrieval', id: 10 },

{ name: 'launch-process', category: 'Process', id: 11 },
{ name: 'read-process', category: 'Process', id: 12 },
{ name: 'write-process', category: 'Process', id: 13 },
{ name: 'kill-process', category: 'Process', id: 14 },
{ name: 'list-processes', category: 'Process', id: 15 },
{ name: 'read-terminal', category: 'Process', id: 16 },

{ name: 'web-search', category: 'Web', id: 17 },
{ name: 'web-fetch', category: 'Web', id: 18 },
{ name: 'open-browser', category: 'Web', id: 19 },

{ name: 'view_tasklist', category: 'Tasks', id: 20 },
{ name: 'update_tasks', category: 'Tasks', id: 21 },
{ name: 'add_tasks', category: 'Tasks', id: 22 },
{ name: 'reorganize_tasklist', category: 'Tasks', id: 23 },

{ name: 'render-mermaid', category: 'Advanced', id: 24 },
{ name: 'remember', category: 'Advanced', id: 25 },
{ name: 'grep-search', category: 'Advanced', id: 26 },

{ name: 'start_worker_agent', category: 'Workers', id: 27 },
{ name: 'read_worker_state', category: 'Workers', id: 28 },
{ name: 'wait_for_worker_agent', category: 'Workers', id: 29 },
{ name: 'send_instruction_to_worker_agent', category: 'Workers', id: 30 },
{ name: 'read_worker_agent_edits', category: 'Workers', id: 31 },
{ name: 'stop_worker_agent', category: 'Workers', id: 32 },
{ name: 'delete_worker_agent', category: 'Workers', id: 33 }
];

function getCtx(toolName, ctxSize = 3000) {
const results = [];
let searchIndex = 0;

while ((searchIndex = content.indexOf(toolName, searchIndex)) !== -1) {
const start = Math.max(0, searchIndex - ctxSize);
const end = Math.min(content.length, searchIndex + ctxSize);
const ctx = content.substring(start, end);

results.push({
index: searchIndex,
ctx: ctx,
beforeContext: content.substring(start, searchIndex),
afterContext: content.substring(searchIndex + toolName.length, end)
});

searchIndex += toolName.length;
}

return results;
}

function getDesc(toolName) {

const ps = [
`desc="[^"]*${toolName}[^"]*"`,
`desc=\`[^\`]*${toolName}[^\`]*\``,
`desc:\\s*"[^"]*${toolName}[^"]*"`,
`Tool for[^"]*${toolName}[^"]*`,
`This tool[^"]*${toolName}[^"]*`]; const descs = []; ps.forEach(p => { const regex = new RegExp(p, 'gi'); const mes = content.m(regex); if (mes) { mes.forEach(m => { descs.push({ p: p, m: m, cleaned: m.replace(/desc=["'`]?|["'`]$/g, '').trim() }); }); } }); return descs; } function getSchema(toolName) { const schemaPatterns = [`inputSchemaJson=JSON\\.stringify\\([^)]*\\)`,
`type:"object",properties:\\{[^}]*${toolName}[^}]*\\}`,
`properties:\\{[^}]*${toolName}[^}]*\\}`,
`required:\\[[^\\]]*${toolName}[^\\]]*\\]`]; const schemas = []; schemaPatterns.forEach(p => { const regex = new RegExp(p, 'gi'); const mes = content.m(regex); if (mes) { mes.forEach(m => { schemas.push({ p: p, m: m }); }); } }); return schemas; } function getClass(toolName) { const classPatterns = [`class\\s+\\w*${toolName}\\w*\\s+extends\\s+\\w+\\s*\\{[^}]*\\}`,
`class\\s+\\w+\\s+extends\\s+lr\\s*\\{[^}]*${toolName}[^}]*\\}`,
`constructor\\([^)]*\\)\\{[^}]*${toolName}[^}]*\\}`]; const classes = []; classPatterns.forEach(p => { const regex = new RegExp(p, 'gi'); const mes = content.m(regex); if (mes) { mes.forEach(m => { classes.push({ p: p, m: m }); }); } }); return classes; } function getPrompts(toolName) { const promptPatterns = [`[A-Z][^.!?]*${toolName}[^.!?]*[.!?]`,
`Use this tool[^.!?]*${toolName}[^.!?]*[.!?]`,
`This tool[^.!?]*${toolName}[^.!?]*[.!?]`,
`IMPORTANT[^.!?]*${toolName}[^.!?]*[.!?]`,
`Note[^.!?]*${toolName}[^.!?]*[.!?]`]; const prompts = []; promptPatterns.forEach(p => { const regex = new RegExp(p, 'gi'); const mes = content.m(regex); if (mes) { mes.forEach(m => { if (m.length > 50) { prompts.push({ p: p, m: m.trim() }); } }); } }); return prompts; } if (!fs.existsSync('tools')) { fs.mkdirSync('tools'); } TOOLS.forEach(tool => { log(`🔍 Extracting ${tool.name} (${tool.id}/33)...`); const toolData = { name: tool.name, id: tool.id, category: tool.category, ctxs: getCtx(tool.name), descs: getDesc(tool.name), schemas: getSchema(tool.name), classes: getClass(tool.name), prompts: getPrompts(tool.name), extractionDate: new Date().toISOString() }; const toolDoc = genDoc(toolData); const filename =`tools/${tool.name.replace(/[^a-zA-Z0-9]/g, '_')}.md`;

write(filename, toolDoc);
log(`✅ Created ${filename}`);
});

function genDoc(toolData) {
return `# ${t.name.toUpperCase()} - COMPLETE TOOL DOCUMENTATION ## 🎯 TOOL OVERVIEW - **Tool Name**: ${t.name} - **Tool ID**: ${t.id} - **Category**: ${t.category} - **Extraction Date**: ${t.extractionDate} ## 📋 TOOL DESCRIPTIONS ${t.descs.map((desc, i) =>`
### Description ${i + 1}
**Pattern**: \`${desc.p}\`
**Raw Match**: \`${desc.m}\`
**Cleaned**: ${desc.cleaned}
`).join('\n')}

## 🔧 TOOL SCHEMAS
${t.schemas.map((schema, i) => `
### Schema ${i + 1}
**Pattern**: \`${schema.p}\`
\`\`\`javascript
${schema.m}
\`\`\`
`).join('\n')}

## 🏗️ CLASS DEFINITIONS
${t.classes.map((cls, i) => `
### Class ${i + 1}
**Pattern**: \`${cls.p}\`
\`\`\`javascript
${cls.m}
\`\`\`
`).join('\n')}

## 💬 RELATED PROMPTS
${t.prompts.map((prompt, i) => `
### Prompt ${i + 1}
**Pattern**: \`${prompt.p}\`
> ${prompt.m}
`).join('\n')} ## 🔍 CONTEXT ANALYSIS **Total Occurrences**: ${t.ctxs.length} ${t.ctxs.map((ctx, i) =>`
### Context ${i + 1} (Index: ${ctx.index})
\`\`\`
${ctx.ctx}
\`\`\`
`).join('\n')}

---
*Generated by Ultimate Tool Extractor - Making History!*
`;
}

log('\n🎉 ULTIMATE TOOL EXTRACTION COMPLETE!');
log(`📁 Created ${TOOLS.length} individual tool documents in ./tools/`);
log('🔥 HISTORY HAS BEEN MADE! 🔥');