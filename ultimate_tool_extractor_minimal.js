const TOOLS=[
{n:'str-replace-editor',c:'Core',i:1},{n:'view',c:'Core',i:2},{n:'save-file',c:'Core',i:3},
{n:'remove-files',c:'Core',i:4},{n:'diagnostics',c:'Core',i:5},{n:'codebase-retrieval',c:'Context',i:6},
{n:'git-commit-retrieval',c:'Context',i:7},{n:'memory-retrieval',c:'Context',i:8},
{n:'view-range-untruncated',c:'Context',i:9},{n:'search-untruncated',c:'Context',i:10},
{n:'launch-process',c:'Process',i:11},{n:'read-process',c:'Process',i:12},{n:'write-process',c:'Process',i:13},
{n:'kill-process',c:'Process',i:14},{n:'list-processes',c:'Process',i:15},{n:'read-terminal',c:'Process',i:16},
{n:'web-search',c:'Web',i:17},{n:'web-fetch',c:'Web',i:18},{n:'open-browser',c:'Web',i:19},
{n:'view_tasklist',c:'Tasks',i:20},{n:'update_tasks',c:'Tasks',i:21},{n:'add_tasks',c:'Tasks',i:22},
{n:'reorganize_tasklist',c:'Tasks',i:23},{n:'render-mermaid',c:'Advanced',i:24},{n:'remember',c:'Advanced',i:25},
{n:'grep-search',c:'Advanced',i:26},{n:'start_worker_agent',c:'Workers',i:27},{n:'read_worker_state',c:'Workers',i:28},
{n:'wait_for_worker_agent',c:'Workers',i:29},{n:'send_instruction_to_worker_agent',c:'Workers',i:30},
{n:'read_worker_agent_edits',c:'Workers',i:31},{n:'stop_worker_agent',c:'Workers',i:32},{n:'delete_worker_agent',c:'Workers',i:33}
];
// Minimal extraction logic
const fs=require('fs'),content=fs.readFileSync('extension.js','utf8');
function extract(tool){const r=[];let i=0;while((i=content.indexOf(tool.n,i))!==-1){r.push(content.substring(Math.max(0,i-500),Math.min(content.length,i+500)));i+=tool.n.length;}return r;}
TOOLS.forEach(t=>{console.log(`${t.n}: ${extract(t).length} occurrences`);});