const fs=require('fs');const path=require('path');const content=fs.readFileSync('extension.js','utf8');const TOOLS=[{n:'str-replace-editor',c:'Core',id:1},{n:'view',c:'Core',id:2},{n:'save-file',c:'Core',id:3},{n:'remove-files',c:'Core',id:4},{n:'diagnostics',c:'Core',id:5},{n:'codebase-retrieval',c:'Context',id:6},{n:'git-commit-retrieval',c:'Context',id:7},{n:'memory-retrieval',c:'Context',id:8},{n:'view-range-untruncated',c:'Context',id:9},{n:'search-untruncated',c:'Context',id:10},{n:'launch-process',c:'Process',id:11},{n:'read-process',c:'Process',id:12},{n:'write-process',c:'Process',id:13},{n:'kill-process',c:'Process',id:14},{n:'list-processes',c:'Process',id:15},{n:'read-terminal',c:'Process',id:16},{n:'web-search',c:'Web',id:17},{n:'web-fetch',c:'Web',id:18},{n:'open-browser',c:'Web',id:19},{n:'view_tasklist',c:'Tasks',id:20},{n:'update_tasks',c:'Tasks',id:21},{n:'add_tasks',c:'Tasks',id:22},{n:'reorganize_tasklist',c:'Tasks',id:23},{n:'render-mermaid',c:'Advanced',id:24},{n:'remember',c:'Advanced',id:25},{n:'grep-search',c:'Advanced',id:26},{n:'start_worker_agent',c:'Workers',id:27},{n:'read_worker_state',c:'Workers',id:28},{n:'wait_for_worker_agent',c:'Workers',id:29},{n:'send_instruction_to_worker_agent',c:'Workers',id:30},{n:'read_worker_agent_edits',c:'Workers',id:31},{n:'stop_worker_agent',c:'Workers',id:32},{n:'delete_worker_agent',c:'Workers',id:33}];function extractToolContext(toolName,contextSize = 3000){const results=[];let searchIndex = 0;while((searchIndex = content.indexOf(toolName,searchIndex))!== -1){const start=Math.max(0,searchIndex - contextSize);const end=Math.min(content.l,searchIndex + contextSize);const context=content.substring(start,end);results.push({index:searchIndex,context:context,beforeContext:content.substring(start,searchIndex),afterContext:content.substring(searchIndex + toolName.l,end)});searchIndex += toolName.l;}return results;}function extractToolDescription(toolName){const patterns=[`description="[^"]*${toolName}[^"]*"`,`description=\`[^\`]*${toolName}[^\`]*\``,`description:\\s*"[^"]*${toolName}[^"]*"`,`Tool for[^"]*${toolName}[^"]*`,`This tool[^"]*${toolName}[^"]*` ];const descriptions=[];patterns.forEach(pattern =>{const regex=new RegExp(pattern,'gi');const matches=content.m(regex);if(matches){matches.forEach(match =>{descriptions.push({pattern:pattern,match:match,cleaned:match.replace(/description=["'`]?|["'`]$/g,'').trim()});});}});return descriptions;}function extractToolSchema(toolName){const schemaPatterns=[`inputSchemaJson=JSON\\.stringify\\([^)]*\\)`,`type:"object",properties:\\{[^}]*${toolName}[^}]*\\}`,`properties:\\{[^}]*${toolName}[^}]*\\}`,`required:\\[[^\\]]*${toolName}[^\\]]*\\]` ];const schemas=[];schemaPatterns.forEach(pattern =>{const regex=new RegExp(pattern,'gi');const matches=content.m(regex);if(matches){matches.forEach(match =>{schemas.push({pattern:pattern,match:match});});}});return schemas;}function extractToolClass(toolName){const classPatterns=[`class\\s+\\w*${toolName}\\w*\\s+extends\\s+\\w+\\s*\\{[^}]*\\}`,`class\\s+\\w+\\s+extends\\s+lr\\s*\\{[^}]*${toolName}[^}]*\\}`,`constructor\\([^)]*\\)\\{[^}]*${toolName}[^}]*\\}` ];const classes=[];classPatterns.forEach(pattern =>{const regex=new RegExp(pattern,'gi');const matches=content.m(regex);if(matches){matches.forEach(match =>{classes.push({pattern:pattern,match:match});});}});return classes;}function extractToolPrompts(toolName){const promptPatterns=[`[A-Z][^.!?]*${toolName}[^.!?]*[.!?]`,`Use this tool[^.!?]*${toolName}[^.!?]*[.!?]`,`This tool[^.!?]*${toolName}[^.!?]*[.!?]`,`IMPORTANT[^.!?]*${toolName}[^.!?]*[.!?]`,`Note[^.!?]*${toolName}[^.!?]*[.!?]` ];const prompts=[];promptPatterns.forEach(pattern =>{const regex=new RegExp(pattern,'gi');const matches=content.m(regex);if(matches){matches.forEach(match =>{if(match.l > 50){prompts.push({pattern:pattern,match:match.trim()});}});}});return prompts;}if(!fs.existsSync('tools')){fs.mkdirSync('tools');}TOOLS.forEach(tool =>{...`);const toolData={name:tool.n,id:tool.i,category:tool.c,contexts:extractToolContext(tool.n),descriptions:extractToolDescription(tool.n),schemas:extractToolSchema(tool.n),classes:extractToolClass(tool.n),prompts:extractToolPrompts(tool.n),extractionDate:new Date().toISOString()};const toolDoc=generateToolDocument(toolData);const filename=`tools/${tool.n.replace(/[^a-zA-Z0-9]/g,'_')}.md`;fs.writeFileSync(filename,toolDoc);});function generateToolDocument(toolData){return `# ${toolData.n.toUpperCase()}- COMPLETE TOOL DOCUMENTATION ## 🎯 TOOL OVERVIEW - **Tool Name**: ${toolData.n}- **Tool ID**: ${toolData.i}- **Category**: ${toolData.c}- **Extraction Date**: ${toolData.extractionDate}## 📋 TOOL DESCRIPTIONS ${toolData.descriptions.map((desc,i)=> ` ### Description ${i + 1}**Pattern**: \`${desc.p}\` **Raw Match**: \`${desc.m}\` **Cleaned**: ${desc.cleaned}`).join('\n')}## 🔧 TOOL SCHEMAS ${toolData.schemas.map((schema,i)=> ` ### Schema ${i + 1}**Pattern**: \`${schema.p}\` \`\`\`javascript ${schema.m}\`\`\` `).join('\n')}## 🏗️ CLASS DEFINITIONS ${toolData.classes.map((cls,i)=> ` ### Class ${i + 1}**Pattern**: \`${cls.p}\` \`\`\`javascript ${cls.m}\`\`\` `).join('\n')}## 💬 RELATED PROMPTS ${toolData.prompts.map((prompt,i)=> ` ### Prompt ${i + 1}**Pattern**: \`${prompt.p}\` > ${prompt.m}`).join('\n')}## 🔍 CONTEXT ANALYSIS **Total Occurrences**: ${toolData.contexts.l}${toolData.contexts.map((ctx,i)=> ` ### Context ${i + 1}(Index: ${ctx.x})\`\`\` ${ctx.ctx}\`\`\` `).join('\n')}--- *Generated by Ultimate Tool Extractor - Making History!* `;}