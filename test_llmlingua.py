#!/usr/bin/env python3
"""
LLMLingua Compression Test for ultimate_tool_extractor.js

This script tests LLMLingua compression on the JavaScript file to demonstrate
token reduction capabilities while maintaining semantic integrity.
"""

import os
import json
import time
from pathlib import Path

def install_llmlingua():
    """Install LLMLingua if not already installed"""
    try:
        from llmlingua import PromptCompressor
        print("✅ LLMLingua already installed")
        return True
    except ImportError:
        print("📦 Installing LLMLingua...")
        os.system("pip install llmlingua")
        try:
            from llmlingua import PromptCompressor
            print("✅ LLMLingua installed successfully")
            return True
        except ImportError:
            print("❌ Failed to install LLMLingua")
            return False

def read_js_file(filepath="ultimate_tool_extractor.js"):
    """Read the JavaScript file content"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"📄 Read {len(content)} characters from {filepath}")
        return content
    except FileNotFoundError:
        print(f"❌ File {filepath} not found")
        return None

def count_tokens_estimate(text):
    """Rough token count estimation (1 token ≈ 4 characters)"""
    return len(text) // 4

def test_compression_rates(compressor, content):
    """Test different compression rates"""
    rates = [0.2, 0.3, 0.5, 0.7, 0.8]
    results = []
    
    original_tokens = count_tokens_estimate(content)
    print(f"\n🔢 Original estimated tokens: {original_tokens}")
    print(f"📏 Original character count: {len(content)}")
    
    for rate in rates:
        print(f"\n🔄 Testing compression rate: {rate}")
        start_time = time.time()
        
        try:
            compressed_result = compressor.compress_prompt(
                content,
                rate=rate,
                force_tokens=False,
                drop_consecutive=True
            )
            
            compressed_content = compressed_result['compressed_prompt']
            compression_time = time.time() - start_time
            
            compressed_tokens = count_tokens_estimate(compressed_content)
            actual_compression_ratio = len(compressed_content) / len(content)
            token_reduction = (original_tokens - compressed_tokens) / original_tokens
            
            result = {
                'target_rate': rate,
                'actual_compression_ratio': actual_compression_ratio,
                'original_chars': len(content),
                'compressed_chars': len(compressed_content),
                'original_tokens_est': original_tokens,
                'compressed_tokens_est': compressed_tokens,
                'token_reduction_pct': token_reduction * 100,
                'compression_time': compression_time,
                'compressed_content': compressed_content[:500] + "..." if len(compressed_content) > 500 else compressed_content
            }
            
            results.append(result)
            
            print(f"  ✅ Compressed: {len(content)} → {len(compressed_content)} chars")
            print(f"  📊 Actual ratio: {actual_compression_ratio:.2f}")
            print(f"  🎯 Token reduction: {token_reduction*100:.1f}%")
            print(f"  ⏱️  Time: {compression_time:.2f}s")
            
        except Exception as e:
            print(f"  ❌ Error at rate {rate}: {str(e)}")
            
    return results

def save_results(results, original_content):
    """Save compression results to files"""
    # Save detailed results as JSON
    with open('compression_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # Save a readable report
    with open('compression_report.md', 'w', encoding='utf-8') as f:
        f.write("# LLMLingua Compression Test Results\n\n")
        f.write(f"**Original File**: ultimate_tool_extractor.js\n")
        f.write(f"**Test Date**: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## Compression Results\n\n")
        f.write("| Target Rate | Actual Ratio | Char Reduction | Token Reduction | Time (s) |\n")
        f.write("|-------------|--------------|----------------|-----------------|----------|\n")
        
        for result in results:
            char_reduction = (1 - result['actual_compression_ratio']) * 100
            f.write(f"| {result['target_rate']:.1f} | {result['actual_compression_ratio']:.2f} | {char_reduction:.1f}% | {result['token_reduction_pct']:.1f}% | {result['compression_time']:.2f} |\n")
        
        f.write("\n## Sample Compressed Output (Rate 0.5)\n\n")
        best_result = next((r for r in results if r['target_rate'] == 0.5), results[0] if results else None)
        if best_result:
            f.write("```javascript\n")
            f.write(best_result['compressed_content'])
            f.write("\n```\n")
    
    print(f"\n💾 Results saved to:")
    print(f"  📊 compression_results.json")
    print(f"  📝 compression_report.md")

def main():
    """Main test function"""
    print("🔥 LLMLingua Compression Test - Making History! 🔥\n")
    
    # Install LLMLingua if needed
    if not install_llmlingua():
        return
    
    # Import after installation
    from llmlingua import PromptCompressor
    
    # Read the JavaScript file
    content = read_js_file()
    if not content:
        return
    
    # Initialize compressor
    print("\n🚀 Initializing LLMLingua compressor...")
    try:
        compressor = PromptCompressor(
            model_name="microsoft/llmlingua-2-xlm-roberta-large-meetingbank",
            use_llmlingua2=True,
            device_map="auto"
        )
        print("✅ Compressor initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize compressor: {e}")
        print("🔄 Trying with default model...")
        try:
            compressor = PromptCompressor()
            print("✅ Default compressor initialized")
        except Exception as e2:
            print(f"❌ Failed with default model: {e2}")
            return
    
    # Test different compression rates
    results = test_compression_rates(compressor, content)
    
    if results:
        # Save results
        save_results(results, content)
        
        # Print summary
        print(f"\n🎉 Compression test complete!")
        print(f"📈 Tested {len(results)} compression rates")
        
        best_result = min(results, key=lambda x: abs(x['target_rate'] - 0.5))
        print(f"\n🏆 Best result (rate ~0.5):")
        print(f"  📉 Size reduction: {(1-best_result['actual_compression_ratio'])*100:.1f}%")
        print(f"  🎯 Token reduction: {best_result['token_reduction_pct']:.1f}%")
        print(f"  ⚡ Compression time: {best_result['compression_time']:.2f}s")
    else:
        print("❌ No successful compressions")

if __name__ == "__main__":
    main()
