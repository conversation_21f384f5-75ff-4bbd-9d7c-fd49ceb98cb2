# 🔥 WRITE-PROCESS - ULTIMATE COMPLETE TOOL DOCUMENTATION 🔥

## 🎯 TOOL OVERVIEW
- **Tool Name**: write-process
- **Tool ID**: 13
- **Category**: Process Management - Process Input Writing and Control
- **Extraction Date**: 2025-07-16T20:42:00.000Z
- **System Class**: Process Communication System
- **Logger**: Integrated with process interaction system
- **Safety Classification**: `Nt.Unsafe` - Can modify process state

## 🚀 BREAKTHROUGH DISCOVERY: ADVANCED PROCESS INPUT CONTROL

**MAJOR FINDING**: This tool provides comprehensive process input capabilities:
1. **Process Input Writing** - Send input to launched processes
2. **Terminal ID System** - Unique identification of process terminals
3. **Interactive Control** - Full interactive process control
4. **Real-time Communication** - Live process interaction
5. **Command Injection** - Send commands and data to processes

---

## 📋 COMPLETE TOOL DESCRIPTION FROM SOURCE CODE

**Full Description Extracted:**

```
Write input to a terminal.
```

**Critical Capabilities:**
- ✅ **Process input writing** - Send stdin input to launched processes
- ✅ **Terminal ID system** - Unique process terminal identification
- ✅ **Interactive control** - Full interactive process communication
- ✅ **Real-time interaction** - Immediate input sending
- ✅ **Command injection** - Send commands and data to processes

## 🔧 COMPLETE TOOL SCHEMA FROM SOURCE CODE

**Complete JSON Schema Extracted:**

```json
{
  "type": "object",
  "properties": {
    "terminal_id": {
      "type": "integer",
      "description": "Terminal ID to write to."
    },
    "input_text": {
      "type": "string",
      "description": "Text to write to the process's stdin."
    }
  },
  "required": ["terminal_id", "input_text"]
}
```

## 🚨 CRITICAL FEATURES

### 🛡️ INPUT CONTROL
- **Terminal ID System**: Unique identification of process terminals
- **Text Input**: Send arbitrary text to process stdin
- **Interactive Support**: Full interactive process communication
- **Real-time Sending**: Immediate input transmission
- **Command Injection**: Send commands and control sequences

### ⚡ PERFORMANCE OPTIMIZATION
- **Efficient Writing**: Fast input transmission to processes
- **Memory Management**: Efficient handling of input data
- **Terminal Integration**: Seamless terminal communication
- **State Management**: Maintains process communication state
- **Error Handling**: Robust error handling for failed writes

## 🎯 BEST PRACTICES

### ✅ RECOMMENDED USAGE
1. **Include newlines for commands** - Most processes expect newline-terminated input
2. **Use appropriate terminal IDs** - Must match launched process
3. **Send commands sequentially** - Wait for responses between commands
4. **Handle process state** - Ensure process is ready for input
5. **Combine with read-process** - Monitor output after sending input

### ❌ AVOID THESE PATTERNS
1. **Don't use invalid terminal IDs** - Must match launched process
2. **Avoid sending to completed processes** - Check process state first
3. **Don't flood with input** - Send input at appropriate intervals
4. **Avoid malformed commands** - Ensure proper command syntax
5. **Don't ignore process responses** - Monitor output after input

## 📊 PERFORMANCE CHARACTERISTICS

### ⚡ OPTIMIZATION FEATURES
- **Terminal ID System**: Efficient process terminal identification
- **Direct Input**: Fast stdin input transmission
- **Interactive Support**: Real-time process communication
- **Error Recovery**: Robust error handling for communication failures
- **State Tracking**: Process communication state management

## 🔗 RELATED TOOLS

### 🤝 COMPLEMENTARY TOOLS
- **launch-process**: Launch processes to write to
- **read-process**: Read output after sending input
- **kill-process**: Terminate processes
- **list-processes**: View all active processes
- **read-terminal**: Read from VSCode terminal

---

## 🏆 CONCLUSION

The **write-process** tool is essential for interactive process control and communication. With its terminal ID system and direct stdin access, it provides comprehensive process input capabilities for development workflows.

**Key Strengths:**
- ✅ Direct process input control
- ✅ Terminal ID system for process identification
- ✅ Interactive process communication
- ✅ Real-time input transmission
- ✅ Command injection capabilities
- ✅ Robust error handling

This tool is **ESSENTIAL** for controlling interactive processes, sending commands to applications, and managing process communication workflows.

---

*Generated by Ultimate Tool Extractor - Complete write-process Tool Documentation*
