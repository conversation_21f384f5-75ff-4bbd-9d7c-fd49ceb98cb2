const fs = require('fs');const path = require('path');const content = fs.readFileSync('extension.js','utf8');console.log('🔥 ULTIMATE TOOL EXTRACTION - MAKING HISTORY! 🔥\n');const TOOLS = [{n:'str-replace-editor',c:'Core',i:1},{n:'view',c:'Core',i:2},{n:'save-file',c:'Core',i:3},{n:'remove-files',c:'Core',i:4},{n:'diagnostics',c:'Core',i:5},{n:'codebase-retrieval',c:'Context',i:6},{n:'git-commit-retrieval',c:'Context',i:7},{n:'memory-retrieval',c:'Context',i:8},{n:'view-range-untruncated',c:'Context',i:9},{n:'search-untruncated',c:'Context',i:10},{n:'launch-process',c:'Process',i:11},{n:'read-process',c:'Process',i:12},{n:'write-process',c:'Process',i:13},{n:'kill-process',c:'Process',i:14},{n:'list-processes',c:'Process',i:15},{n:'read-terminal',c:'Process',i:16},{n:'web-search',c:'Web',i:17},{n:'web-fetch',c:'Web',i:18},{n:'open-browser',c:'Web',i:19},{n:'view_tasklist',c:'Tasks',i:20},{n:'update_tasks',c:'Tasks',i:21},{n:'add_tasks',c:'Tasks',i:22},{n:'reorganize_tasklist',c:'Tasks',i:23},{n:'render-mermaid',c:'Advanced',i:24},{n:'remember',c:'Advanced',i:25},{n:'grep-search',c:'Advanced',i:26},{n:'start_worker_agent',c:'Workers',i:27},{n:'read_worker_state',c:'Workers',i:28},{n:'wait_for_worker_agent',c:'Workers',i:29},{n:'send_instruction_to_worker_agent',c:'Workers',i:30},{n:'read_worker_agent_edits',c:'Workers',i:31},{n:'stop_worker_agent',c:'Workers',i:32},{n:'delete_worker_agent',c:'Workers',i:33}];function extractToolContext(toolName,contextSize = 3000){const results = [];let searchIndex = 0;while((searchIndex = content.indexOf(toolName,searchIndex))!== -1){const start = Math.max(0,searchIndex - contextSize);const end = Math.min(content.l,searchIndex + contextSize);const context = content.substring(start,end);results.push({x:searchIndex,ctx:context,beforeContext:content.substring(start,searchIndex),afterContext:content.substring(searchIndex + toolName.l,end)});searchIndex += toolName.l}return results}function extractToolDescription(toolName){const patterns = [`description="[^"]*${toolName}[^"]*"`,`description=\`[^\`]*${toolName}[^\`]*\``,`d:\\s*"[^"]*${toolName}[^"]*"`,`Tool for[^"]*${toolName}[^"]*`,`This tool[^"]*${toolName}[^"]*` ];const descriptions = [];patterns.forEach(pattern =>{const regex = new RegExp(pattern,'gi');const matches = content.m(regex);if(matches){matches.forEach(match =>{descriptions.push({p:pattern,m:match,cleaned:match.replace(/description=["'`]?|["'`]$/g,'').trim()})})}});return descriptions}function extractToolSchema(toolName){const schemaPatterns = [`inputSchemaJson=JSON\\.stringify\\([^)]*\\)`,`t:"object",properties:\\{[^}]*${toolName}[^}]*\\}`,`properties:\\{[^}]*${toolName}[^}]*\\}`,`required:\\[[^\\]]*${toolName}[^\\]]*\\]` ];const schemas = [];schemaPatterns.forEach(pattern =>{const regex = new RegExp(pattern,'gi');const matches = content.m(regex);if(matches){matches.forEach(match =>{schemas.push({p:pattern,m:match})})}});return schemas}function extractToolClass(toolName){const classPatterns = [`class\\s+\\w*${toolName}\\w*\\s+extends\\s+\\w+\\s*\\{[^}]*\\}`,`class\\s+\\w+\\s+extends\\s+lr\\s*\\{[^}]*${toolName}[^}]*\\}`,`constructor\\([^)]*\\)\\{[^}]*${toolName}[^}]*\\}` ];const classes = [];classPatterns.forEach(pattern =>{const regex = new RegExp(pattern,'gi');const matches = content.m(regex);if(matches){matches.forEach(match =>{classes.push({p:pattern,m:match})})}});return classes}function extractToolPrompts(toolName){const promptPatterns = [`[A-Z][^.!?]*${toolName}[^.!?]*[.!?]`,`Use this tool[^.!?]*${toolName}[^.!?]*[.!?]`,`This tool[^.!?]*${toolName}[^.!?]*[.!?]`,`IMPORTANT[^.!?]*${toolName}[^.!?]*[.!?]`,`Note[^.!?]*${toolName}[^.!?]*[.!?]` ];const prompts = [];promptPatterns.forEach(pattern =>{const regex = new RegExp(pattern,'gi');const matches = content.m(regex);if(matches){matches.forEach(match =>{if(match.l > 50){prompts.push({p:pattern,m:match.trim()})}})}});return prompts}if(!fs.existsSync('tools')){fs.mkdirSync('tools')}TOOLS.forEach(tool =>{console.log(`🔍 Extracting ${tool.n}(${tool.i}/33)...`);const toolData ={n:tool.n,i:tool.i,c:tool.c,contexts:extractToolContext(tool.n),descriptions:extractToolDescription(tool.n),schemas:extractToolSchema(tool.n),classes:extractToolClass(tool.n),prompts:extractToolPrompts(tool.n),extractionDate:new Date().toISOString()};const toolDoc = generateToolDocument(toolData);const filename = `tools/${tool.n.replace(/[^a-zA-Z0-9]/g,'_')}.md`;fs.writeFileSync(filename,toolDoc);console.log(`✅ Created ${filename}`)});function generateToolDocument(toolData){return `# ${toolData.n.toUpperCase()}- COMPLETE TOOL DOCUMENTATION ## 🎯 TOOL OVERVIEW - **Tool Name**: ${toolData.n}- **Tool ID**: ${toolData.i}- **Category**: ${toolData.c}- **Extraction Date**: ${toolData.extractionDate}## 📋 TOOL DESCRIPTIONS ${toolData.descriptions.map((desc,i)=> ` ### Description ${i + 1}**Pattern**: \`${desc.p}\` **Raw Match**: \`${desc.m}\` **Cleaned**: ${desc.cleaned}`).join('\n')}## 🔧 TOOL SCHEMAS ${toolData.schemas.map((schema,i)=> ` ### Schema ${i + 1}**Pattern**: \`${schema.p}\` \`\`\`javascript ${schema.m}\`\`\` `).join('\n')}## 🏗️ CLASS DEFINITIONS ${toolData.classes.map((cls,i)=> ` ### Class ${i + 1}**Pattern**: \`${cls.p}\` \`\`\`javascript ${cls.m}\`\`\` `).join('\n')}## 💬 RELATED PROMPTS ${toolData.prompts.map((prompt,i)=> ` ### Prompt ${i + 1}**Pattern**: \`${prompt.p}\` > ${prompt.m}`).join('\n')}## 🔍 CONTEXT ANALYSIS **Total Occurrences**: ${toolData.contexts.l}${toolData.contexts.map((ctx,i)=> ` ### Context ${i + 1}(Index: ${ctx.x})\`\`\` ${ctx.ctx}\`\`\` `).join('\n')}--- *Generated by Ultimate Tool Extractor - Making History!* `}console.log('\n🎉 ULTIMATE TOOL EXTRACTION COMPLETE!');console.log(`📁 Created ${TOOLS.l}individual tool documents in ./tools/`);console.log('🔥 HISTORY HAS BEEN MADE! 🔥');