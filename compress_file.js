const fs = require('fs');
const zlib = require('zlib');
const path = require('path');

/**
 * Compresses a file using gzip and encodes it as base64
 * @param {string} inputFile - Path to the input file
 * @param {string} outputFile - Path to save the compressed result
 */
function compressFileToBase64(inputFile, outputFile) {
    try {
        console.log(`🗜️  Compressing ${inputFile}...`);
        
        // Read the input file
        const inputData = fs.readFileSync(inputFile, 'utf8');
        const originalSize = Buffer.byteLength(inputData, 'utf8');
        
        // Compress using gzip
        const compressed = zlib.gzipSync(inputData);
        const compressedSize = compressed.length;
        
        // Encode to base64
        const base64Encoded = compressed.toString('base64');
        const base64Size = Buffer.byteLength(base64Encoded, 'utf8');
        
        // Create output object with metadata
        const output = {
            metadata: {
                originalFile: inputFile,
                originalSize: originalSize,
                compressedSize: compressedSize,
                base64Size: base64Size,
                compressionRatio: (compressedSize / originalSize * 100).toFixed(2) + '%',
                base64Ratio: (base64Size / originalSize * 100).toFixed(2) + '%',
                timestamp: new Date().toISOString(),
                encoding: 'gzip+base64'
            },
            compressedData: base64Encoded
        };
        
        // Save to output file
        fs.writeFileSync(outputFile, JSON.stringify(output, null, 2));
        
        console.log(`✅ Compression complete!`);
        console.log(`📊 Original size: ${originalSize} bytes`);
        console.log(`📊 Compressed size: ${compressedSize} bytes (${output.metadata.compressionRatio})`);
        console.log(`📊 Base64 size: ${base64Size} bytes (${output.metadata.base64Ratio})`);
        console.log(`💾 Saved to: ${outputFile}`);
        
        return output;
        
    } catch (error) {
        console.error(`❌ Error compressing file: ${error.message}`);
        throw error;
    }
}

/**
 * Decompresses a base64+gzip compressed file
 * @param {string} compressedFile - Path to the compressed file
 * @param {string} outputFile - Path to save the decompressed result
 */
function decompressFromBase64(compressedFile, outputFile) {
    try {
        console.log(`🔓 Decompressing ${compressedFile}...`);
        
        // Read the compressed file
        const compressedData = JSON.parse(fs.readFileSync(compressedFile, 'utf8'));
        
        // Decode from base64
        const compressed = Buffer.from(compressedData.compressedData, 'base64');
        
        // Decompress using gzip
        const decompressed = zlib.gunzipSync(compressed);
        const originalData = decompressed.toString('utf8');
        
        // Save decompressed data
        fs.writeFileSync(outputFile, originalData);
        
        console.log(`✅ Decompression complete!`);
        console.log(`📊 Metadata:`, compressedData.metadata);
        console.log(`💾 Saved to: ${outputFile}`);
        
        return originalData;
        
    } catch (error) {
        console.error(`❌ Error decompressing file: ${error.message}`);
        throw error;
    }
}

// Main execution
if (require.main === module) {
    const inputFile = 'ultimate_tool_extractor.js';
    const compressedFile = 'ultimate_tool_extractor_compressed.json';
    const testDecompressedFile = 'ultimate_tool_extractor_test_decompressed.js';
    
    console.log('🚀 Starting file compression process...\n');
    
    // Compress the file
    const result = compressFileToBase64(inputFile, compressedFile);
    
    console.log('\n🧪 Testing decompression...');
    
    // Test decompression
    const decompressed = decompressFromBase64(compressedFile, testDecompressedFile);
    
    // Verify the decompressed content matches original
    const original = fs.readFileSync(inputFile, 'utf8');
    if (original === decompressed) {
        console.log('✅ Verification successful! Decompressed content matches original.');
    } else {
        console.log('❌ Verification failed! Decompressed content does not match original.');
    }
    
    console.log('\n🎉 Process complete!');
    console.log(`📁 Compressed file: ${compressedFile}`);
    console.log(`📁 Test decompressed file: ${testDecompressedFile}`);
}

module.exports = {
    compressFileToBase64,
    decompressFromBase64
};
