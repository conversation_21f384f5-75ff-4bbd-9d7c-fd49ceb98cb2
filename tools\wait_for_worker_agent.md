# WAIT_FOR_WORKER_AGENT - COMPLETE TOOL DOCUMENTATION

## 🎯 TOOL OVERVIEW
- **Tool Name**: wait_for_worker_agent
- **Tool ID**: 29
- **Category**: Worker Agent System
- **Extraction Date**: 2025-07-16T19:09:44.314Z

## 📋 TOOL DESCRIPTIONS


## 🔧 TOOL SCHEMAS

### Schema 1
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{information_request:{type:"string",description:"A description of the information you need."}},required:["information_request"]})
```


### Schema 2
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{file_paths:{type:"array",description:"The paths of the files to remove.",items:{type:"string"}}},required:["file_paths"]})
```


### Schema 3
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{path:{type:"string",description:"The path of the file to save."},file_content:{type:"string",description:"The content of the file."},add_last_line_newline:{type:"boolean",description:"Whether to add a newline at the end of the file (default: true)
```


### Schema 4
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{command:{type:"string",enum:["str_replace","insert"],description:"The commands to run. Allowed options are: 'str_replace', 'insert'."},path:{description:"Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.",type:"string"},insert_line_1:{description:"Required parameter of `insert` command. The line number after which to insert the new string. This line number is relative to the state of the file before any insertions in the current tool call have been applied.",type:"integer"},new_str_1:{description:"Required parameter of `str_replace` command containing the new string. Can be an empty string to delete content. Required parameter of `insert` command containing the string to insert.",type:"string"},old_str_1:{description:"Required parameter of `str_replace` command containing the string in `path` to replace.",type:"string"},old_str_start_line_number_1:{description:"The line number of the first line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.",type:"integer"},old_str_end_line_number_1:{description:"The line number of the last line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.",type:"integer"}},required:["command","path"]})
```


### Schema 5
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{command:{type:"string",enum:["str_replace","insert"],description:"The commands to run. Allowed options are: 'str_replace', 'insert'."},path:{description:"Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.",type:"string"},insert_line_entries:{description:"Required parameter of `insert` command. A list of entries to insert. Each entry is a dictionary with keys `insert_line` and `new_str`.",type:"array",items:{type:"object",properties:{insert_line:{description:"The line number after which to insert the new string. This line number is relative to the state of the file before any insertions in the current tool call have been applied.",type:"integer"},new_str:{description:"The string to insert. Can be an empty string.",type:"string"}},required:["insert_line","new_str"]}},str_replace_entries:{description:"Required parameter of `str_replace` command. A list of entries to replace. Each entry is a dictionary with keys `old_str`, `old_str_start_line_number`, `old_str_end_line_number` and `new_str`. `old_str` from different entries should not overlap.",type:"array",items:{type:"object",properties:{old_str:{description:"The string in `path` to replace.",type:"string"},old_str_start_line_number:{description:"The line number of the first line of `old_str` in the file. This is used to disambiguate between multiple occurrences of `old_str` in the file.",type:"integer"},old_str_end_line_number:{description:"The line number of the last line of `old_str` in the file. This is used to disambiguate between multiple occurrences of `old_str` in the file.",type:"integer"},new_str:{description:"The string to replace `old_str` with. Can be an empty string to delete content.",type:"string"}},required:["old_str","new_str","old_str_start_line_number","old_str_end_line_number"]}}},required:["command","path"]})
```


### Schema 6
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{type:{type:"string",description:"Type of path to view. Allowed options are: 'file', 'directory'.",enum:["file","directory"]},path:{description:"Full path to file or directory relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.",type:"string"},view_range:{description:"Optional parameter when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [501, 1000] will show lines from 501 to 1000. Indices are 1-based and inclusive. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.",type:"array",items:{type:"integer"}},search_query_regex:{description:"Optional parameter for files only. The regex pattern to search for. Only use core regex syntax common to JavaScript and Rust. See the regex syntax guide in the tool description. When specified, only lines matching the pattern (plus context lines)
```


### Schema 7
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{command:{type:"string",enum:["str_replace","insert"],description:"The commands to run. Allowed options are: 'str_replace', 'insert'."},path:{description:"Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.",type:"string"},instruction_reminder:{description:`Reminder to limit edits to at most ${this.maxLines} lines. Should be exactly this string: '${this.instructions}'`,type:"string"},insert_line_1:{description:"Required parameter of `insert` command. The line number after which to insert the new string. This line number is relative to the state of the file before any insertions in the current tool call have been applied.",type:"integer"},new_str_1:{description:"Required parameter of `str_replace` command containing the new string. Can be an empty string to delete content. Required parameter of `insert` command containing the string to insert.",type:"string"},old_str_1:{description:"Required parameter of `str_replace` command containing the string in `path` to replace.",type:"string"},old_str_start_line_number_1:{description:"The line number of the first line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.",type:"integer"},old_str_end_line_number_1:{description:"The line number of the last line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.",type:"integer"}},required:["command","path","instruction_reminder"]})
```


### Schema 8
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{url:{type:"string",description:"The URL to fetch."}},required:["url"]})
```


### Schema 9
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{memory:{type:"string",description:"The concise (1 sentence)
```


### Schema 10
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{},required:[]})
```


### Schema 11
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{tasks:{type:"array",description:"Array of tasks to update. Each task should have a task_id and the properties to update.",items:{type:"object",properties:{task_id:{type:"string",description:"The UUID of the task to update."},state:{type:"string",enum:["NOT_STARTED","IN_PROGRESS","CANCELLED","COMPLETE"],description:"New task state. Use NOT_STARTED for [ ], IN_PROGRESS for [/], CANCELLED for [-], COMPLETE for [x]."},name:{type:"string",description:"New task name."},description:{type:"string",description:"New task description."}},required:["task_id"]}}},required:["tasks"]})
```


### Schema 12
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{markdown:{type:"string",description:"The markdown representation of the task list to update. Should be in the format specified by the view_tasklist tool. New tasks should have a UUID of 'NEW_UUID'. Must contain exactly one root task with proper hierarchy using dash indentation."}},required:["markdown"]})
```


### Schema 13
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{tasks:{type:"array",description:"Array of tasks to create. Each task should have name and description.",items:{type:"object",properties:{name:{type:"string",description:"The name of the new task."},description:{type:"string",description:"The description of the new task."},parent_task_id:{type:"string",description:"UUID of the parent task if this should be a subtask."},after_task_id:{type:"string",description:"UUID of the task after which this task should be inserted."},state:{type:"string",enum:["NOT_STARTED","IN_PROGRESS","CANCELLED","COMPLETE"],description:"Initial state of the task. Defaults to NOT_STARTED."}},required:["name","description"]}}},required:["tasks"]})
```


### Schema 14
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{reference_id:{type:"string",description:"The reference ID of the truncated content (found in the truncation footer)
```


### Schema 15
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{reference_id:{type:"string",description:"The reference ID of the truncated content (found in the truncation footer)
```


### Schema 16
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{diagram_definition:{type:"string",description:"The Mermaid diagram definition code to render"},title:{type:"string",description:"Optional title for the diagram",default:"Mermaid Diagram"}},required:["diagram_definition"]})
```


### Schema 17
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{directory_absolute_path:{type:"string",description:"Absolute path to the directory to search in."},query:{type:"string",description:"The regex pattern to search for. Only use core regex syntax common to JavaScript and Rust. See the regex syntax guide in the tool description"},case_sensitive:{type:"boolean",description:"Optional parameter for case sensitivity when matching `query`. By default matching is not case sensitive."},files_include_glob_pattern:{type:"string",description:"Optional glob pattern for files to include"},files_exclude_glob_pattern:{type:"string",description:"Optional glob pattern for files to exclude"},context_lines_before:{type:"integer",description:`Optional parameter for number of lines of context to include before the match. Default is ${this._defaultContextNumLinesBefore}`},context_lines_after:{type:"integer",description:`Optional parameter for number of lines of context to include after the match. Default is ${this._defaultContextNumLinesAfter}`},disable_ignore_files:{type:"boolean",description:"Optional parameter to disable ignore logic which skips hidden files, files matching .gitignore, etc."}},required:["directory_absolute_path","query"]})
```


### Schema 18
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{workers:{type:"array",description:"Array of worker configurations to start. Workers will complete tasks independently, and their results must be manually retrieved and pulled into your local workspace using ReadWorkerAgentEditsTool. If empty array, no workers will be started.",items:{type:"object",properties:{summary:{type:"string",description:"A short description of the task to delegate to the worker agent (results will be retrievable later for manual integration)
```


### Schema 19
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{worker_agent_ids:{type:"array",description:"Array of worker agent IDs for INSTANTANEOUS status check only. If empty array, reads state for all available worker agents. For waiting/monitoring, use WaitForWorkerAgentTool instead.",items:{type:"string",description:"ID of the worker agent for immediate status snapshot (not for waiting)
```


### Schema 20
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{wait_operations:{type:"array",description:"Array of wait operations for integrated monitoring and waiting. Use this instead of polling ReadWorkerStateTool repeatedly.",items:{type:"object",properties:{worker_agent_id:{type:"string",description:"ID of the worker agent to wait for completion with integrated monitoring"},target_status:{type:"string",enum:["idle","failed"],description:"Status to wait for (idle=completed successfully, failed=error occurred)
```


### Schema 21
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{instruction_operations:{type:"array",description:"Array of instruction operations to perform. If empty array, no instructions will be sent.",items:{type:"object",properties:{summary:{type:"string",description:"A short description of what this instruction is asking the worker agent to do (results will need to be manually retrieved later)
```


### Schema 22
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{worker_agent_ids:{type:"array",description:"Array of worker agent IDs to stop. If empty array, stops all available worker agents.",items:{type:"string",description:"ID of the worker agent to stop (ensure you've retrieved any needed changes first)
```


### Schema 23
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{worker_agent_ids:{type:"array",description:"Array of worker agent IDs to delete. If empty array, deletes all available worker agents.",items:{type:"string",description:"ID of the worker agent to delete (ensure you've retrieved any needed changes first)
```


### Schema 24
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{worker_agent_ids:{type:"array",description:"Array of worker agent IDs whose completed file changes you want to collect for manual integration into your local workspace. If empty array, reads edits for all available worker agents.",items:{type:"string",description:"ID of the worker agent whose file changes you want to retrieve and manually apply"}}},required:["worker_agent_ids"]})
```


### Schema 25
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{command:{type:"string",description:"The shell command to execute."},wait:{type:"boolean",description:"Whether to wait for the command to complete."},max_wait_seconds:{type:"number",description:"Number of seconds to wait for the command to complete. Only relevant when wait=true. 10 minutes may be a good default: increase from there if needed."},cwd:{type:"string",description:"Required parameter. Absolute path to the working directory for the command."}},required:["command","wait","max_wait_seconds","cwd"]})
```


### Schema 26
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{terminal_id:{type:"integer",description:"Terminal ID to kill."}},required:["terminal_id"]})
```


### Schema 27
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{terminal_id:{type:"integer",description:"Terminal ID to read from."},wait:{type:"boolean",description:"Whether to wait for the command to complete."},max_wait_seconds:{type:"number",description:"Number of seconds to wait for the command to complete. Only relevant when wait=true. 1 minute may be a good default: increase from there if needed."}},required:["terminal_id","wait","max_wait_seconds"]})
```


### Schema 28
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{terminal_id:{type:"integer",description:"Terminal ID to write to."},input_text:{type:"string",description:"Text to write to the process's stdin."}},required:["terminal_id","input_text"]})
```


### Schema 29
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{},required:[]})
```


### Schema 30
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{file_path:{type:"string",description:"The path of the file to read."}},required:["file_path"]})
```


### Schema 31
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{file_path:{type:"string",description:"The path of the file to edit."},edit_summary:{type:"string",description:"A brief description of the edit to be made. 1-2 sentences."},detailed_edit_description:{type:"string",description:"A detailed and precise description of the edit. Can include natural language and code snippets."}},required:["file_path","edit_summary","detailed_edit_description"]})
```


### Schema 32
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{url:{type:"string",description:"The URL to open in the browser."}},required:["url"]})
```


### Schema 33
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{paths:{type:"array",items:{type:"string"},description:"Required list of file paths to get issues for from the IDE."}},required:["paths"]})
```


### Schema 34
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{information_request:{type:"string",description:"A description of the information you need."}},required:["information_request"]})
```


### Schema 35
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{information_request:{type:"string",description:"A description of the information you need."}},required:["information_request"]})
```


### Schema 36
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{only_selected:{type:"boolean",description:"Whether to read only the selected text in the terminal."}},required:[]})
```


### Schema 37
**Pattern**: `inputSchemaJson=JSON\.stringify\([^)]*\)`
```javascript
inputSchemaJson=JSON.stringify({type:"object",properties:{command:{type:"string",description:"The shell command to execute."}},required:["command"]})
```


## 🏗️ CLASS DEFINITIONS


## 💬 RELATED PROMPTS


## 🔍 CONTEXT ANALYSIS
**Total Occurrences**: 1


### Context 1 (Index: 4104539)
```
Instance()}function k_e(){AB.reset()}var vB=class e extends ls{static defaultMaxRecords=1e4;static defaultBatchSize=1e3;static defaultUploadMsec=1e4;constructor(t,r,n){super("ToolUseRequestEventReporter",t??e.defaultMaxRecords,r??e.defaultUploadMsec,n??e.defaultBatchSize)}reportEvent(t,r,n,i,s,a,o,c,l,u,d){let[h,p]=us(Date.now());this.report({requestId:t,toolName:r,toolUseId:n,toolInput:i,toolOutputIsError:s,toolRunDurationMs:a,isMcpTool:o,conversationId:c,chatHistoryLength:l,eventTimeSec:h,eventTimeNsec:p,toolRequestId:u,toolOutputLen:d})}performUpload(t){return $u().logToolUseRequestEvent(t)}};var yB=class{_pluginFileStore;_logger=ot("ToolApprovalConfigManager");constructor(t){this._pluginFileStore=t}async getToolApprovalConfig(t){let{mode:r,toolId:n}=t;if(r)try{let i=I_e(r,n),s=await this._pluginFileStore.loadAsset(i);if(!s)return;let a=new TextDecoder().decode(s);return JSON.parse(a)}catch(i){this._logger.error(`Failed to load tool approval config: ${wt(i,!0)}`);return}}async setToolApprovalConfig(t){let{mode:r,toolId:n,config:i}=t;try{let s=I_e(r,n),a=JSON.stringify(i,null,2),o=new TextEncoder().encode(a);await this._pluginFileStore.saveAsset(s,o),this._logger.info(`Saved tool approval config: ${s}`)}catch(s){throw this._logger.error(`Failed to save tool approval config: ${wt(s,!0)}`),s}}};function mTt(e){return`${e.hostName}-${String(e.toolId)}`}function I_e(e,t){return`tool-configs/approval/${e}/${mTt(t)}.json`}var Pn;(function(e){e.shell="shell",e.webFetch="web-fetch",e.strReplaceEditor="str-replace-editor",e.codebaseRetrieval="codebase-retrieval",e.removeFiles="remove-files",e.remember="remember",e.view="view",e.saveFile="save-file",e.viewTaskList="view_tasklist",e.viewRangeUntruncated="view-range-untruncated",e.searchUntruncated="search-untruncated",e.reorganizeTaskList="reorganize_tasklist",e.updateTasks="update_tasks",e.addTasks="add_tasks",e.renderMermaid="render-mermaid",e.grepSearch="grep-search"})(Pn||(Pn={}));var VB={strReplaceEditor:"str_replace_editor_tool",backendEditTool:"backend_edit_tool"};var M_e={enabled:!1,volume:.5},ew={enabled:!0},ja;(function(e){e.hasEverUsedAgent="hasEverUsedAgent",e.hasEverUsedRemoteAgent="hasEverUsedRemoteAgent",e.soundSettings="soundSettings",e.swarmModeSettings="swarmModeSettings"})(ja||(ja={}));var tMe=Ce(Dc());var qn;(function(e){e.readFile="read-file",e.saveFile="save-file",e.editFile="edit-file",e.clarify="clarify",e.onboardingSubAgent="onboarding-sub-agent",e.launchProcess="launch-process",e.killProcess="kill-process",e.readProcess="read-process",e.writeProcess="write-process",e.listProcesses="list-processes",e.waitProcess="wait-process",e.openBrowser="open-browser",e.strReplaceEditor="str-replace-editor",e.remember="remember",e.diagnostics="diagnostics",e.setupScript="setup-script",e.readTerminal="read-terminal",e.gitCommitRetrieval="git-commit-retrieval",e.memoryRetrieval="memory-retrieval",e.startWorkerAgent="start_worker_agent",e.readWorkerState="read_worker_state",e.waitForWorkerAgent="wait_for_worker_agent",e.sendInstructionToWorkerAgent="send_instruction_to_worker_agent",e.stopWorkerAgent="stop_worker_agent",e.deleteWorkerAgent="delete_worker_agent",e.readWorkerAgentEdits="read_worker_agent_edits",e.beachheadSubAgent="beachhead-sub-agent"})(qn||(qn={}));var xo;(function(e){e.remoteToolHost="remoteToolHost",e.localToolHost="localToolHost",e.sidecarToolHost="sidecarToolHost",e.mcpHost="mcpHost"})(xo||(xo={}));var r1;(function(e){e[e.ContentText=0]="ContentText",e[e.ContentImage=1]="ContentImage"})(r1||(r1={}));var Nt;(function(e){e[e.Unsafe=0]="Unsafe",e[e.Safe=1]="Safe",e[e.Check=2]="Check"})(Nt||(Nt={}));var lr=class{name;toolSafety;constructor(t,r){this.name=t,this.toolSafety=r}version=1},Js;(function(e){e[e.Unknown=0]="Unknown",e[e.WebSearch=1]="WebSearch",e[e.GitHubApi=8]="GitHubApi",e[e.Linear=12]="Linear",e[e.Jira=13]="Jira",e[e.Confluence=14]="Confluence",e[e.Notion=15]="Notion",e[e.Supabase=16]="Supabase",e[e.Glean=17]="Glean"})(Js||(Js={}));var hv;(function(e){e[e.UnknownStatus=0]="UnknownStatus",e[e.Available=1]="Available",e[e.UserConfigRequired=2]="UserConfigRequired"})(hv||(hv={}));var T_e;(function(e){e.Never="never",e.Always="always",e.InputDependent="inputDependent"})(T_e||(T_e={}));var Kn;(function(e){e.assertEqual=i=>i;function t(i){}e.assertIs=t;function r(i){throw new Error}e.assertNever=r,e.arrayToEnum=i=>{let s={};for(let a of i)s[a]=a;return s},e.getValidEnumValues=i=>{let s=e.objectKeys(i).filter(o=>typeof i[i[o]]!="number"),a={};for(let o of s)a[o]=i[o];return e.objectValues(a)},e.objectValues=i=>e.objectKeys(i).map(function(s){return i[s]}),e.objectKeys=typeof Object.keys=="function"?i=>Object.keys(i):i=>{let s=[];for(let a in i)Object.prototype.hasOwnProperty.call(i,a)&&s.push(a);return s},e.find=(i,s)=>{for(let a of i)if(s(a))return a},e.isInteger=typeof Number.isInteger=="function"?i=>Number.isInteger(i):i=>typeof i=="number"&&isFinite(i)&&Math.floor(i)===i;function n(i,s=" | "){return i.map(a=>typeof a=="string"?`'${a}'`:a).join(s)}e.joinValues=n,e.jsonStringifyReplacer=(i,s)=>typeof s=="bigint"?s.toString():s})(Kn||(Kn={}));var Rre;(function(e){e.mergeShapes=(t,r)=>({...t,...r})})(Rre||(Rre={}));var Pt=Kn.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Hg=e=>{switch(typeof e){case"undefined":return Pt.undefined;case"string":return Pt.string;case"number":return isNaN(e)?Pt.nan:Pt.number;case"boolean":return Pt.boolean;case"function":return Pt.function;case"bigint":return Pt.bigint;case"symbol":return Pt.symbol;case"object":return Array.isArray(e)?Pt.array:e===null?Pt.null:e.then&&typeof e.then=="function"&&e.catch&&typeof e.catch=="function"?Pt.promise:typeof Map<"u"&&e instanceof Map?Pt.map:typeof Set<"u"&&e instanceof Set?Pt.set:typeof Date<"u"&&e instanceof Date?Pt.date:Pt.object;default:return Pt.unknown}},pt=Kn.arrayToEnum(["invalid_type","invalid_literal",
```


---
*Generated by Ultimate Tool Extractor - Making History!*
