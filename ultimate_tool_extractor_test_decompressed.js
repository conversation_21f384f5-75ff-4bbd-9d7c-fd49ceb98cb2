const fs = require('fs');
const path = require('path');

// Read the extension.js file
const content = fs.readFileSync('extension.js', 'utf8');

console.log('🔥 ULTIMATE TOOL EXTRACTION - MAKING HISTORY! 🔥\n');

// Complete tool list from our analysis
const TOOLS = [
    // Core Development Tools
    { name: 'str-replace-editor', category: 'Core Development', id: 1 },
    { name: 'view', category: 'Core Development', id: 2 },
    { name: 'save-file', category: 'Core Development', id: 3 },
    { name: 'remove-files', category: 'Core Development', id: 4 },
    { name: 'diagnostics', category: 'Core Development', id: 5 },
    
    // Context and Retrieval Tools
    { name: 'codebase-retrieval', category: 'Context and Retrieval', id: 6 },
    { name: 'git-commit-retrieval', category: 'Context and Retrieval', id: 7 },
    { name: 'memory-retrieval', category: 'Context and Retrieval', id: 8 },
    { name: 'view-range-untruncated', category: 'Context and Retrieval', id: 9 },
    { name: 'search-untruncated', category: 'Context and Retrieval', id: 10 },
    
    // Process Management Tools
    { name: 'launch-process', category: 'Process Management', id: 11 },
    { name: 'read-process', category: 'Process Management', id: 12 },
    { name: 'write-process', category: 'Process Management', id: 13 },
    { name: 'kill-process', category: 'Process Management', id: 14 },
    { name: 'list-processes', category: 'Process Management', id: 15 },
    { name: 'read-terminal', category: 'Process Management', id: 16 },
    
    // Web and External Tools
    { name: 'web-search', category: 'Web and External', id: 17 },
    { name: 'web-fetch', category: 'Web and External', id: 18 },
    { name: 'open-browser', category: 'Web and External', id: 19 },
    
    // Task and Project Management
    { name: 'view_tasklist', category: 'Task Management', id: 20 },
    { name: 'update_tasks', category: 'Task Management', id: 21 },
    { name: 'add_tasks', category: 'Task Management', id: 22 },
    { name: 'reorganize_tasklist', category: 'Task Management', id: 23 },
    
    // Advanced Features
    { name: 'render-mermaid', category: 'Advanced Features', id: 24 },
    { name: 'remember', category: 'Advanced Features', id: 25 },
    { name: 'grep-search', category: 'Advanced Features', id: 26 },
    
    // Worker Agent System
    { name: 'start_worker_agent', category: 'Worker Agent System', id: 27 },
    { name: 'read_worker_state', category: 'Worker Agent System', id: 28 },
    { name: 'wait_for_worker_agent', category: 'Worker Agent System', id: 29 },
    { name: 'send_instruction_to_worker_agent', category: 'Worker Agent System', id: 30 },
    { name: 'read_worker_agent_edits', category: 'Worker Agent System', id: 31 },
    { name: 'stop_worker_agent', category: 'Worker Agent System', id: 32 },
    { name: 'delete_worker_agent', category: 'Worker Agent System', id: 33 }
];

// Advanced extraction functions
function extractToolContext(toolName, contextSize = 3000) {
    const results = [];
    let searchIndex = 0;
    
    // Search for all occurrences of the tool name
    while ((searchIndex = content.indexOf(toolName, searchIndex)) !== -1) {
        const start = Math.max(0, searchIndex - contextSize);
        const end = Math.min(content.length, searchIndex + contextSize);
        const context = content.substring(start, end);
        
        results.push({
            index: searchIndex,
            context: context,
            beforeContext: content.substring(start, searchIndex),
            afterContext: content.substring(searchIndex + toolName.length, end)
        });
        
        searchIndex += toolName.length;
    }
    
    return results;
}

function extractToolDescription(toolName) {
    // Look for description patterns
    const patterns = [
        `description="[^"]*${toolName}[^"]*"`,
        `description=\`[^\`]*${toolName}[^\`]*\``,
        `description:\\s*"[^"]*${toolName}[^"]*"`,
        `Tool for[^"]*${toolName}[^"]*`,
        `This tool[^"]*${toolName}[^"]*`
    ];
    
    const descriptions = [];
    
    patterns.forEach(pattern => {
        const regex = new RegExp(pattern, 'gi');
        const matches = content.match(regex);
        if (matches) {
            matches.forEach(match => {
                descriptions.push({
                    pattern: pattern,
                    match: match,
                    cleaned: match.replace(/description=["'`]?|["'`]$/g, '').trim()
                });
            });
        }
    });
    
    return descriptions;
}

function extractToolSchema(toolName) {
    // Look for schema patterns
    const schemaPatterns = [
        `inputSchemaJson=JSON\\.stringify\\([^)]*\\)`,
        `type:"object",properties:\\{[^}]*${toolName}[^}]*\\}`,
        `properties:\\{[^}]*${toolName}[^}]*\\}`,
        `required:\\[[^\\]]*${toolName}[^\\]]*\\]`
    ];
    
    const schemas = [];
    
    schemaPatterns.forEach(pattern => {
        const regex = new RegExp(pattern, 'gi');
        const matches = content.match(regex);
        if (matches) {
            matches.forEach(match => {
                schemas.push({
                    pattern: pattern,
                    match: match
                });
            });
        }
    });
    
    return schemas;
}

function extractToolClass(toolName) {
    // Look for class definitions
    const classPatterns = [
        `class\\s+\\w*${toolName}\\w*\\s+extends\\s+\\w+\\s*\\{[^}]*\\}`,
        `class\\s+\\w+\\s+extends\\s+lr\\s*\\{[^}]*${toolName}[^}]*\\}`,
        `constructor\\([^)]*\\)\\{[^}]*${toolName}[^}]*\\}`
    ];
    
    const classes = [];
    
    classPatterns.forEach(pattern => {
        const regex = new RegExp(pattern, 'gi');
        const matches = content.match(regex);
        if (matches) {
            matches.forEach(match => {
                classes.push({
                    pattern: pattern,
                    match: match
                });
            });
        }
    });
    
    return classes;
}

function extractToolPrompts(toolName) {
    // Look for prompt-like text patterns
    const promptPatterns = [
        `[A-Z][^.!?]*${toolName}[^.!?]*[.!?]`,
        `Use this tool[^.!?]*${toolName}[^.!?]*[.!?]`,
        `This tool[^.!?]*${toolName}[^.!?]*[.!?]`,
        `IMPORTANT[^.!?]*${toolName}[^.!?]*[.!?]`,
        `Note[^.!?]*${toolName}[^.!?]*[.!?]`
    ];
    
    const prompts = [];
    
    promptPatterns.forEach(pattern => {
        const regex = new RegExp(pattern, 'gi');
        const matches = content.match(regex);
        if (matches) {
            matches.forEach(match => {
                if (match.length > 50) { // Only include substantial prompts
                    prompts.push({
                        pattern: pattern,
                        match: match.trim()
                    });
                }
            });
        }
    });
    
    return prompts;
}

// Create tools directory
if (!fs.existsSync('tools')) {
    fs.mkdirSync('tools');
}

// Process each tool
TOOLS.forEach(tool => {
    console.log(`🔍 Extracting ${tool.name} (${tool.id}/33)...`);
    
    const toolData = {
        name: tool.name,
        id: tool.id,
        category: tool.category,
        contexts: extractToolContext(tool.name),
        descriptions: extractToolDescription(tool.name),
        schemas: extractToolSchema(tool.name),
        classes: extractToolClass(tool.name),
        prompts: extractToolPrompts(tool.name),
        extractionDate: new Date().toISOString()
    };
    
    // Create individual tool document
    const toolDoc = generateToolDocument(toolData);
    const filename = `tools/${tool.name.replace(/[^a-zA-Z0-9]/g, '_')}.md`;
    
    fs.writeFileSync(filename, toolDoc);
    console.log(`✅ Created ${filename}`);
});

function generateToolDocument(toolData) {
    return `# ${toolData.name.toUpperCase()} - COMPLETE TOOL DOCUMENTATION

## 🎯 TOOL OVERVIEW
- **Tool Name**: ${toolData.name}
- **Tool ID**: ${toolData.id}
- **Category**: ${toolData.category}
- **Extraction Date**: ${toolData.extractionDate}

## 📋 TOOL DESCRIPTIONS
${toolData.descriptions.map((desc, i) => `
### Description ${i + 1}
**Pattern**: \`${desc.pattern}\`
**Raw Match**: \`${desc.match}\`
**Cleaned**: ${desc.cleaned}
`).join('\n')}

## 🔧 TOOL SCHEMAS
${toolData.schemas.map((schema, i) => `
### Schema ${i + 1}
**Pattern**: \`${schema.pattern}\`
\`\`\`javascript
${schema.match}
\`\`\`
`).join('\n')}

## 🏗️ CLASS DEFINITIONS
${toolData.classes.map((cls, i) => `
### Class ${i + 1}
**Pattern**: \`${cls.pattern}\`
\`\`\`javascript
${cls.match}
\`\`\`
`).join('\n')}

## 💬 RELATED PROMPTS
${toolData.prompts.map((prompt, i) => `
### Prompt ${i + 1}
**Pattern**: \`${prompt.pattern}\`
> ${prompt.match}
`).join('\n')}

## 🔍 CONTEXT ANALYSIS
**Total Occurrences**: ${toolData.contexts.length}

${toolData.contexts.map((ctx, i) => `
### Context ${i + 1} (Index: ${ctx.index})
\`\`\`
${ctx.context}
\`\`\`
`).join('\n')}

---
*Generated by Ultimate Tool Extractor - Making History!*
`;
}

console.log('\n🎉 ULTIMATE TOOL EXTRACTION COMPLETE!');
console.log(`📁 Created ${TOOLS.length} individual tool documents in ./tools/`);
console.log('🔥 HISTORY HAS BEEN MADE! 🔥');
