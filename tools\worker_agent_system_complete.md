# 🔥 WORKER AGENT SYSTEM - COMPLETE TOOL DOCUMENTATION 🔥

## 🎯 SYSTEM OVERVIEW
- **System Name**: Worker Agent System
- **Category**: Distributed Computing & Remote Orchestration
- **Extraction Date**: 2025-07-16T21:30:00.000Z
- **Total Tools**: 6 Core Tools
- **System Class**: Distributed Worker Management System

## 🚀 BREAKTHROUGH DISCOVERY: DISTRIBUTED WORKER ORCHESTRATION

**MAJOR FINDING**: This is a complete distributed worker system with:
1. **Worker Lifecycle Management** - Start, monitor, control, and cleanup workers
2. **Real-time Communication** - Send instructions to running workers
3. **Result Retrieval** - <PERSON>ull completed work back to local workspace
4. **Status Monitoring** - Track worker progress and completion
5. **Distributed Execution** - Run tasks across multiple remote environments

---

## 🛠️ COMPLETE WORKER AGENT TOOL SUITE

### 1. 🚀 START_WORKER_AGENT_TOOL
**Tool ID**: `qn.startWorkerAgent`
**Safety**: `Nt.Unsafe` (Creates remote resources)

**Purpose**: Launch one or more worker agents in isolated environments

**Schema**:
```json
{
  "type": "object",
  "properties": {
    "workers": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "summary": {"type": "string"},
          "initial_prompt": {"type": "string"},
          "repository_url": {"type": "string"},
          "git_ref": {"type": "string"},
          "model_id": {"type": "string"}
        },
        "required": ["summary", "initial_prompt"]
      }
    }
  },
  "required": ["workers"]
}
```

### 2. 📊 READ_WORKER_STATE_TOOL
**Tool ID**: `qn.readWorkerState`
**Safety**: `Nt.Safe` (Read-only)

**Purpose**: Get instantaneous snapshot of worker agent states

**Schema**:
```json
{
  "type": "object",
  "properties": {
    "worker_agent_ids": {
      "type": "array",
      "items": {"type": "string"}
    }
  },
  "required": ["worker_agent_ids"]
}
```

### 3. ⏳ WAIT_FOR_WORKER_AGENT_TOOL
**Tool ID**: `qn.waitForWorkerAgent`
**Safety**: `Nt.Safe` (Monitoring only)

**Purpose**: Wait for worker agents to complete with integrated monitoring

**Schema**:
```json
{
  "type": "object",
  "properties": {
    "wait_operations": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "worker_agent_id": {"type": "string"},
          "target_status": {"type": "string", "enum": ["idle", "failed"]},
          "timeout_seconds": {"type": "number"}
        },
        "required": ["worker_agent_id", "target_status"]
      }
    }
  },
  "required": ["wait_operations"]
}
```

### 4. 💬 SEND_INSTRUCTION_TO_WORKER_AGENT_TOOL
**Tool ID**: `qn.sendInstructionToWorkerAgent`
**Safety**: `Nt.Unsafe` (Modifies worker state)

**Purpose**: Send new instructions to running worker agents

**Schema**:
```json
{
  "type": "object",
  "properties": {
    "instructions": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "worker_agent_id": {"type": "string"},
          "instruction": {"type": "string"}
        },
        "required": ["worker_agent_id", "instruction"]
      }
    }
  },
  "required": ["instructions"]
}
```

### 5. 📥 READ_WORKER_AGENT_EDITS_TOOL
**Tool ID**: `qn.readWorkerAgentEdits`
**Safety**: `Nt.Safe` (Read-only)

**Purpose**: Retrieve file changes made by worker agents

**Schema**:
```json
{
  "type": "object",
  "properties": {
    "worker_agent_ids": {
      "type": "array",
      "items": {"type": "string"}
    }
  },
  "required": ["worker_agent_ids"]
}
```

### 6. 🗑️ DELETE_WORKER_AGENT_TOOL
**Tool ID**: `qn.deleteWorkerAgent`
**Safety**: `Nt.Unsafe` (Destroys resources)

**Purpose**: Delete worker agents and clean up resources

**Schema**:
```json
{
  "type": "object",
  "properties": {
    "worker_agent_ids": {
      "type": "array",
      "items": {"type": "string"}
    }
  },
  "required": ["worker_agent_ids"]
}
```

---

## 🔄 WORKER LIFECYCLE WORKFLOW

### Phase 1: Worker Creation
```javascript
// 1. Start workers
startWorkerAgent({
  workers: [{
    summary: "Implement authentication system",
    initial_prompt: "Create JWT-based auth with user registration and login",
    repository_url: "https://github.com/user/repo.git",
    git_ref: "main"
  }]
})
```

### Phase 2: Monitoring & Communication
```javascript
// 2. Monitor status
readWorkerState({worker_agent_ids: ["worker-123"]})

// 3. Send additional instructions
sendInstructionToWorkerAgent({
  instructions: [{
    worker_agent_id: "worker-123",
    instruction: "Add password reset functionality"
  }]
})

// 4. Wait for completion
waitForWorkerAgent({
  wait_operations: [{
    worker_agent_id: "worker-123",
    target_status: "idle",
    timeout_seconds: 600
  }]
})
```

### Phase 3: Result Collection
```javascript
// 5. Retrieve completed work
readWorkerAgentEdits({worker_agent_ids: ["worker-123"]})

// 6. Clean up
deleteWorkerAgent({worker_agent_ids: ["worker-123"]})
```

---

## 🏗️ CORE IMPLEMENTATION DETAILS

### Message Type System
```javascript
const ma = {
  TEXT: 0,           // Text instructions
  TOOL_RESULT: 1,    // Tool execution results
  IMAGE: 2,          // Image content
  IDE_STATE: 4,      // IDE state information
  EDIT_EVENTS: 5,    // Edit event data
  CHECKPOINT_REF: 6, // Checkpoint reference
  FILE: 8            // File content
};
```

### Worker Status Types
```javascript
const WorkerStatus = {
  UNSPECIFIED: 0,
  STARTING: 1,
  RUNNING: 2,
  IDLE: 3,        // Completed successfully
  FAILED: 4,      // Error occurred
  PENDING: 5
};
```

### API Server Methods
```javascript
// Core API methods used by worker tools
await apiServer.createRemoteAgent(config, request, modelId, setupScript, async)
await apiServer.listRemoteAgents()
await apiServer.getRemoteAgentChatHistory(agentId, offset)
await apiServer.remoteAgentChat(agentId, messagePayload)
await apiServer.interruptRemoteAgent(agentId)
await apiServer.deleteRemoteAgent(agentId)
await apiServer.getRemoteAgentOverviewsStream(filter, signal)
```

---

## 🎯 USAGE PATTERNS

### Pattern 1: Single Worker Task
```javascript
// Start → Wait → Retrieve → Cleanup
const workers = await startWorkerAgent({workers: [config]})
const completion = await waitForWorkerAgent({wait_operations: [waitConfig]})
const edits = await readWorkerAgentEdits({worker_agent_ids: [workerId]})
await deleteWorkerAgent({worker_agent_ids: [workerId]})
```

### Pattern 2: Multi-Worker Coordination
```javascript
// Start multiple workers for different tasks
const workers = await startWorkerAgent({
  workers: [frontendConfig, backendConfig, testingConfig]
})

// Monitor all workers
const states = await readWorkerState({worker_agent_ids: workerIds})

// Wait for all to complete
const completions = await waitForWorkerAgent({
  wait_operations: workerIds.map(id => ({
    worker_agent_id: id,
    target_status: "idle",
    timeout_seconds: 1200
  }))
})
```

### Pattern 3: Interactive Worker Control
```javascript
// Start worker
const worker = await startWorkerAgent({workers: [config]})

// Send dynamic instructions based on progress
await sendInstructionToWorkerAgent({
  instructions: [{
    worker_agent_id: workerId,
    instruction: "Focus on error handling for the login flow"
  }]
})

// Continue monitoring and adjusting
```

---

---

## 🔧 DETAILED IMPLEMENTATION FROM SOURCE CODE

### StartWorkerAgentTool Implementation
```javascript
// R$ class - StartWorkerAgentTool (From Source Code)
R$ = class extends lr {
  constructor(r, n, i) {
    super(qn.startWorkerAgent, Nt.Unsafe);
    this._apiServer = r;
    this._configListener = n;
    this._globalState = i;
  }

  _logger = Te("StartWorkerAgentTool");

  description = `Start Worker Agents

Start one or more worker agents to delegate specific tasks. Worker agents run in isolated environments and complete their assigned work independently. After completion, YOU MUST use other orchestration tools to retrieve their results and manually pull changes into your local workspace.

Pass an array of worker configurations. Each worker can have different prompts, repositories, and settings. If the array is empty, no workers will be started.

Use this to delegate work that will later be pulled back into your current development environment. Worker agents cannot directly modify your local files - you must retrieve and apply their changes yourself.`;

  async call(r, n, i, s) {
    let requestId = this._apiServer.createRequestId();

    try {
      let workers = r.workers;

      if (!Array.isArray(workers)) {
        return nt("Workers must be an array.", requestId);
      }

      if (workers.length === 0) {
        return Rt(JSON.stringify({
          startedWorkers: [],
          message: "No workers to start."
        }), requestId);
      }

      let results = [];

      // Process each worker configuration
      for (let i = 0; i < workers.length; i++) {
        let worker = workers[i];
        let summary = worker.summary;
        let initialPrompt = worker.initial_prompt;
        let repositoryUrl = worker.repository_url;
        let gitRef = worker.git_ref;
        let modelId = worker.model_id;

        try {
          // Validate required fields
          if (!summary || !summary.trim()) {
            results.push({
              index: i,
              success: false,
              error: "Summary cannot be empty."
            });
            continue;
          }

          if (!initialPrompt || !initialPrompt.trim()) {
            results.push({
              index: i,
              success: false,
              error: "Initial prompt cannot be empty."
            });
            continue;
          }

          // Auto-detect git repository if not provided
          if (!repositoryUrl || !gitRef) {
            try {
              let gitRoot = await U9(); // Find git root
              if (!gitRoot) {
                results.push({
                  index: i,
                  success: false,
                  error: "Could not find git repository. Please ensure you're in a git repository or provide repository_url and git_ref explicitly."
                });
                continue;
              }

              if (!repositoryUrl) {
                let remoteUrl = await Ra("git remote get-url origin", {cwd: gitRoot});
                if (!remoteUrl?.trim()) {
                  results.push({
                    index: i,
                    success: false,
                    error: "Could not determine repository URL. Please provide repository_url explicitly."
                  });
                  continue;
                }
                let url = remoteUrl.trim();
                if (!url.startsWith("https://")) {
                  url = url.replace("ssh://", "").replace("git@", "https://").replace(".com:", ".com/").replace(/\.git$/, "");
                }
                repositoryUrl = url;
              }

              if (!gitRef) {
                let currentBranch = await Ra("git branch --show-current", {cwd: gitRoot});
                if (!currentBranch?.trim()) {
                  results.push({
                    index: i,
                    success: false,
                    error: "Could not determine current branch. Please provide git_ref explicitly."
                  });
                  continue;
                }
                gitRef = currentBranch.trim();
              }
            } catch (error) {
              this._logger.error("Failed to detect git repository information", error);
              results.push({
                index: i,
                success: false,
                error: "Failed to detect git repository information. Please provide repository_url and git_ref explicitly."
              });
              continue;
            }
          }

          // Validate repository URL format
          try {
            new URL(repositoryUrl);
          } catch {
            results.push({
              index: i,
              success: false,
              error: "Invalid repository URL format."
            });
            continue;
          }

          // Create starting files configuration
          let startingFiles = {
            github_commit_ref: {
              repository_url: repositoryUrl,
              git_ref: gitRef
            }
          };

          // Check GitHub authentication on first worker
          if (i === 0) {
            try {
              if (!(await this._apiServer.isUserGithubConfigured()).is_configured) {
                return nt("GitHub authentication is required to start worker agents with remote repositories. Please authenticate with GitHub through the Augment settings panel.", requestId);
              }
            } catch (error) {
              this._logger.error("Failed to check GitHub authentication status", error);
              return nt("Failed to verify GitHub authentication. Please try again or check your connection.", requestId);
            }
          }

          this._logger.debug(`Starting worker agent ${i + 1}/${workers.length} - ${summary} - with prompt: "${initialPrompt}", repo: ${repositoryUrl}, ref: ${gitRef}`);

          // Prepare MCP servers configuration
          let mcpServers = this._configListener.config.mcpServers
            .filter(server => server.type === "stdio")
            .map(server => ({
              command: server.command,
              args: server.args ?? [],
              env: server.env ?? {},
              use_shell_interpolation: server.useShellInterpolation ?? false,
              name: server.name,
              disabled: server.disabled ?? false
            }));

          // Create chat request
          let chatRequest = {
            request_nodes: [{
              id: 1,
              type: ma.TEXT,
              text_node: {
                content: initialPrompt
              }
            }],
            user_guidelines: this._configListener.config.chat.userGuidelines || "",
            workspace_guidelines: "",
            agent_memories: "",
            mcp_servers: mcpServers
          };

          // Get default setup script
          let setupScript = await this._getDefaultSetupScript();

          // Create remote agent
          let agentResult = await this._apiServer.createRemoteAgent(
            startingFiles,
            chatRequest,
            modelId,
            setupScript,
            false
          );

          if (!agentResult.remote_agent_id || agentResult.status === 4) {
            this._logger.error(`Failed to start worker agent ${i + 1}: status ${agentResult.status}`);
            results.push({
              index: i,
              success: false,
              error: `Failed to start worker agent: status ${agentResult.status}`
            });
            continue;
          }

          this._logger.debug(`Successfully started worker agent ${i + 1} with ID: ${agentResult.remote_agent_id}`);

          results.push({
            index: i,
            success: true,
            summary: summary,
            workerAgentId: agentResult.remote_agent_id,
            status: this._getStatusString(agentResult.status)
          });

        } catch (error) {
          this._logger.error(`Failed to start worker agent ${i + 1}`, error);
          results.push({
            index: i,
            success: false,
            error: `Failed to start worker agent: ${error instanceof Error ? error.message : String(error)}`
          });
        }
      }

      // Compile final results
      let finalResults = {
        startedWorkers: results.filter(r => r.success),
        failedWorkers: results.filter(r => !r.success),
        totalRequested: workers.length,
        totalStarted: results.filter(r => r.success).length,
        totalFailed: results.filter(r => !r.success).length
      };

      return Rt(JSON.stringify(finalResults), requestId);

    } catch (error) {
      this._logger.error("Failed to start worker agents", error);
      return nt(`Failed to start worker agents: ${error instanceof Error ? error.message : String(error)}`, requestId);
    }
  }

  async _getDefaultSetupScript() {
    try {
      let lastScript = this._globalState.get("lastRemoteAgentSetupScript");
      if (!lastScript) return undefined;

      let scripts = await new vV(this._globalState).listSetupScripts();
      return scripts.find(script => script.path === lastScript)?.content;
    } catch (error) {
      this._logger.error("Failed to get default setup script", error);
      return undefined;
    }
  }

  _getStatusString(status) {
    switch (status) {
      case 0: return "unspecified";
      case 5: return "pending";
      case 1: return "starting";
      case 2: return "running";
      case 3: return "idle";
      case 4: return "failed";
      default: return "unknown";
    }
  }
}
```

### SendInstructionToWorkerAgentTool Implementation
```javascript
// k$ class - SendInstructionToWorkerAgentTool (From Source Code)
k$ = class extends lr {
  constructor(r, n) {
    super(qn.sendInstructionToWorkerAgent, Nt.Unsafe);
    this._apiServer = r;
    this._configListener = n;
  }

  _logger = Te("SendInstructionToWorkerAgentTool");

  description = `Send Instructions To Worker Agents

Send new instructions/messages to one or more worker agents. After sending instructions, monitor worker status and use ReadWorkerAgentEditsTool to collect completed work for manual integration into your local workspace.

Pass an array of instruction configurations. Each configuration specifies a worker agent ID and the instruction to send. If the array is empty, no instructions will be sent.

YOU MUST manually pull any desired changes into your local workspace using ReadWorkerAgentEditsTool.`;

  async call(r, n, i, s) {
    let requestId = this._apiServer.createRequestId();

    try {
      let instructions = r.instructions;

      if (!Array.isArray(instructions)) {
        return nt("Instructions must be an array.", requestId);
      }

      if (instructions.length === 0) {
        return Rt(JSON.stringify({
          instructionResults: [],
          message: "No instructions to send."
        }), requestId);
      }

      let results = [];

      for (let i = 0; i < instructions.length; i++) {
        let instruction = instructions[i];
        let workerId = instruction.worker_agent_id;
        let message = instruction.instruction;

        try {
          if (!workerId || !workerId.trim()) {
            results.push({
              index: i,
              success: false,
              error: "Worker agent ID cannot be empty."
            });
            continue;
          }

          if (!message || !message.trim()) {
            results.push({
              index: i,
              success: false,
              error: "Instruction cannot be empty."
            });
            continue;
          }

          this._logger.debug(`Sending instruction to worker agent ${i + 1}/${instructions.length}: ${workerId}`);

          let messagePayload = {
            request_nodes: [{
              id: 1,
              type: ma.TEXT,
              text_node: {
                content: message
              }
            }],
            user_guidelines: this._configListener.config.chat.userGuidelines || "",
            workspace_guidelines: "",
            agent_memories: "",
            mcp_servers: []
          };

          await this._apiServer.remoteAgentChat(workerId, messagePayload);

          this._logger.debug(`Successfully sent instruction to worker agent: ${workerId}`);

          results.push({
            index: i,
            success: true,
            workerAgentId: workerId
          });

        } catch (error) {
          this._logger.error(`Failed to send instruction to worker agent ${workerId}`, error);
          results.push({
            index: i,
            success: false,
            error: `Failed to send instruction: ${error instanceof Error ? error.message : String(error)}`
          });
        }
      }

      let finalResults = {
        instructionResults: results.filter(r => r.success),
        failedInstructions: results.filter(r => !r.success),
        totalRequested: instructions.length,
        totalSent: results.filter(r => r.success).length,
        totalFailed: results.filter(r => !r.success).length
      };

      return Rt(JSON.stringify(finalResults), requestId);

    } catch (error) {
      this._logger.error("Failed to send instructions to worker agents", error);
      return nt(`Failed to send instructions to worker agents: ${error instanceof Error ? error.message : String(error)}`, requestId);
    }
  }
}
```

### ReadWorkerAgentEditsTool Implementation
```javascript
// T$ class - ReadWorkerAgentEditsTool (From Source Code)
T$ = class extends lr {
  constructor(r) {
    super(qn.readWorkerAgentEdits, Nt.Safe);
    this._apiServer = r;
  }

  _logger = Te("ReadWorkerAgentEditsTool");

  description = `Retrieve Worker Agent File Changes

Collect and review file changes made by worker agents so you can manually integrate them into your local workspace. Returns detailed information about added, modified, and deleted files that YOU MUST manually apply to your current development environment.

Pass an array of worker agent IDs whose changes you want to retrieve. If the array is empty, collects edits from all available worker agents.

Use this to pull completed work from worker agents for review and manual application to your local workspace. Worker agents cannot directly modify your local files.`;

  async call(r, n, i, s) {
    let requestId = this._apiServer.createRequestId();

    try {
      let workerIds = r.worker_agent_ids;

      if (!Array.isArray(workerIds)) {
        return nt("Worker agent IDs must be an array.", requestId);
      }

      let remoteAgents = (await this._apiServer.listRemoteAgents()).remote_agents;
      let targetIds;

      if (workerIds.length === 0) {
        targetIds = remoteAgents.map(agent => agent.remote_agent_id);
        this._logger.debug(`Reading edits for all ${targetIds.length} worker agents`);
      } else {
        targetIds = workerIds.filter(id => id && id.trim());
        this._logger.debug(`Reading edits for ${targetIds.length} specified worker agents`);
      }

      if (targetIds.length === 0) {
        return Rt(JSON.stringify({
          editResults: [],
          message: "No worker agents to read edits for."
        }), requestId);
      }

      let results = [];

      for (let i = 0; i < targetIds.length; i++) {
        let agentId = targetIds[i];

        try {
          if (!remoteAgents.find(agent => agent.remote_agent_id === agentId)) {
            results.push({
              agentId: agentId,
              success: false,
              error: `Worker agent ${agentId} not found`
            });
            continue;
          }

          this._logger.debug(`Reading edits for worker agent ${i + 1}/${targetIds.length}: ${agentId}`);

          let chatHistory = await this._apiServer.getRemoteAgentChatHistory(agentId, 0);
          let fileEdits = [];

          if (chatHistory.chat_history) {
            for (let exchange of chatHistory.chat_history) {
              if (exchange.changed_files) {
                for (let changedFile of exchange.changed_files) {
                  fileEdits.push({
                    filePath: changedFile.new_path || changedFile.old_path,
                    changeType: this._getFileChangeTypeString(changedFile.change_type),
                    content: changedFile.new_contents
                  });
                }
              }
            }
          }

          results.push({
            agentId: agentId,
            success: true,
            fileEdits: fileEdits
          });

        } catch (error) {
          this._logger.error(`Failed to read edits for worker agent ${agentId}`, error);
          results.push({
            agentId: agentId,
            success: false,
            error: `Failed to read worker agent edits: ${error instanceof Error ? error.message : String(error)}`
          });
        }
      }

      let finalResults = {
        editResults: results.filter(r => r.success),
        failedReads: results.filter(r => !r.success),
        totalRequested: targetIds.length,
        totalSuccessful: results.filter(r => r.success).length,
        totalFailed: results.filter(r => !r.success).length
      };

      return Rt(JSON.stringify(finalResults), requestId);

    } catch (error) {
      this._logger.error("Failed to read worker agent edits", error);
      return nt(`Failed to read worker agent edits: ${error instanceof Error ? error.message : String(error)}`, requestId);
    }
  }

  _getFileChangeTypeString(changeType) {
    switch (changeType) {
      case 0: return "added";
      case 1: return "deleted";
      case 2: return "modified";
      case 3: return "renamed";
      default: return "unknown";
    }
  }
}
```

### WaitForWorkerAgentTool Implementation
```javascript
// x$ class - WaitForWorkerAgentTool (From Source Code)
x$ = class extends lr {
  constructor(r) {
    super(qn.waitForWorkerAgent, Nt.Safe);
    this._apiServer = r;
  }

  _logger = Te("WaitForWorkerAgentTool");

  description = `Wait For Worker Agent Completion (Integrated Waiting & Monitoring)

PREFERRED TOOL for monitoring worker agent progress and waiting for completion. This tool provides integrated waiting with automatic progress monitoring - use this instead of repeatedly calling ReadWorkerStateTool.

This tool:
- Actively waits for worker agents to reach target status ('idle' or 'failed')
- Uses efficient real-time streaming for instant status updates when available
- Falls back to polling with 10-second intervals if streaming is unavailable
- Returns detailed completion information when work is finished
- Handles timeouts gracefully with configurable limits

WHEN TO USE:
- When you need to wait for worker agents to complete their tasks
- For monitoring progress over time (replaces manual polling)
- Before retrieving results with ReadWorkerAgentEditsTool

Pass an array of wait configurations. Each configuration specifies a worker agent ID, target status, and optional timeout (default: 600 seconds).

YOU MUST manually pull any desired changes into your local workspace after completion.`;

  async call(r, n, i, s) {
    let requestId = this._apiServer.createRequestId();

    try {
      let waitOperations = r.wait_operations;

      if (!Array.isArray(waitOperations)) {
        return nt("Wait operations must be an array.", requestId);
      }

      if (waitOperations.length === 0) {
        return Rt(JSON.stringify({
          waitResults: [],
          message: "No wait operations to perform."
        }), requestId);
      }

      // Validate all operations first
      for (let i = 0; i < waitOperations.length; i++) {
        let operation = waitOperations[i];
        let workerId = operation.worker_agent_id;
        let targetStatus = operation.target_status;

        if (!workerId || !workerId.trim()) {
          return nt(`Wait operation ${i}: Worker agent ID cannot be empty.`, requestId);
        }

        if (!["idle", "failed"].includes(targetStatus)) {
          return nt(`Wait operation ${i}: Target status must be 'idle' or 'failed'.`, requestId);
        }
      }

      this._logger.debug(`Starting ${waitOperations.length} wait operations`);

      // Execute all wait operations concurrently
      let waitPromises = waitOperations.map(async (operation, index) => {
        let workerId = operation.worker_agent_id;
        let targetStatus = operation.target_status;
        let timeoutSeconds = operation.timeout_seconds || 600;

        return this._waitForAgentStatusWithStreaming(workerId, targetStatus, timeoutSeconds, index, s);
      });

      let results = await Promise.all(waitPromises);

      let finalResults = {
        waitResults: results.filter(r => r.success),
        failedWaits: results.filter(r => !r.success),
        totalOperations: waitOperations.length,
        totalSuccessful: results.filter(r => r.success).length,
        totalFailed: results.filter(r => !r.success).length
      };

      return Rt(JSON.stringify(finalResults), requestId);

    } catch (error) {
      this._logger.error("Failed to wait for worker agents", error);
      return nt(`Failed to wait for worker agents: ${error instanceof Error ? error.message : String(error)}`, requestId);
    }
  }

  async _waitForAgentStatusWithStreaming(workerId, targetStatus, timeoutMs, index, abortSignal) {
    let startTime = Date.now();
    let timeoutDuration = timeoutMs * 1000;

    try {
      // Check if agent exists and get initial status
      let remoteAgents = (await this._apiServer.listRemoteAgents()).remote_agents;
      let agent = remoteAgents.find(a => a.remote_agent_id === workerId);

      if (!agent) {
        return {
          index: index,
          agentId: workerId,
          success: false,
          error: `Worker agent ${workerId} not found`
        };
      }

      let currentStatus = this._getStatusString(agent.status);

      // If already at target status, return immediately
      if (currentStatus === targetStatus) {
        return {
          index: index,
          agentId: workerId,
          success: true,
          status: currentStatus,
          targetStatus: targetStatus,
          waitedSeconds: 0
        };
      }

      try {
        // Try streaming approach first
        return await this._waitUsingStreaming(workerId, targetStatus, timeoutDuration, startTime, index, abortSignal);
      } catch (error) {
        this._logger.warn(`Streaming failed for agent ${workerId}, falling back to polling:`, error);
        // Fall back to polling
        return await this._waitUsingPolling(workerId, targetStatus, timeoutDuration, startTime, index, abortSignal, 10000);
      }

    } catch (error) {
      return {
        index: index,
        agentId: workerId,
        success: false,
        error: `Failed to wait for worker agent: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  async _waitUsingStreaming(workerId, targetStatus, timeoutDuration, startTime, index, abortSignal) {
    let abortController = new AbortController();
    let combinedSignal = AbortSignal.any([abortSignal, abortController.signal]);

    let timeoutId = setTimeout(() => {
      abortController.abort();
    }, timeoutDuration);

    try {
      let stream = this._apiServer.getRemoteAgentOverviewsStream(undefined, combinedSignal);

      for await (let update of stream) {
        if (combinedSignal.aborted) {
          if (abortSignal.aborted && typeof abortSignal.reason === "string") {
            let reason = abortSignal.reason;
            throw new DOMException(reason, "AbortError");
          }
          if (abortController.signal.aborted) {
            let timeoutReason = "Timeout waiting for worker agent completion";
            throw new DOMException(timeoutReason, "AbortError");
          }
        }

        for (let agentUpdate of update.updates) {
          if (agentUpdate.agent && agentUpdate.agent.remote_agent_id === workerId) {
            let currentStatus = this._getStatusString(agentUpdate.agent.status);

            if (currentStatus === targetStatus) {
              return {
                index: index,
                agentId: workerId,
                success: true,
                status: currentStatus,
                targetStatus: targetStatus,
                waitedSeconds: Math.round((Date.now() - startTime) / 1000)
              };
            }
          }
        }
      }

      return {
        index: index,
        agentId: workerId,
        success: false,
        error: `Stream ended before worker agent ${workerId} reached status ${targetStatus}`
      };

    } finally {
      clearTimeout(timeoutId);
    }
  }

  async _waitUsingPolling(workerId, targetStatus, timeoutDuration, startTime, index, abortSignal, pollInterval = 10000) {
    while (Date.now() - startTime < timeoutDuration) {
      if (abortSignal.aborted) {
        return {
          index: index,
          agentId: workerId,
          success: false,
          error: "Wait operation was cancelled."
        };
      }

      let remoteAgents = (await this._apiServer.listRemoteAgents()).remote_agents;
      let agent = remoteAgents.find(a => a.remote_agent_id === workerId);

      if (!agent) {
        return {
          index: index,
          agentId: workerId,
          success: false,
          error: `Worker agent ${workerId} not found`
        };
      }

      let currentStatus = this._getStatusString(agent.status);

      if (currentStatus === targetStatus) {
        return {
          index: index,
          agentId: workerId,
          success: true,
          status: currentStatus,
          targetStatus: targetStatus,
          waitedSeconds: Math.round((Date.now() - startTime) / 1000)
        };
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    return {
      index: index,
      agentId: workerId,
      success: false,
      error: `Timeout waiting for worker agent ${workerId} to reach status ${targetStatus}`
    };
  }

  _getStatusString(status) {
    switch (status) {
      case 0: return "unspecified";
      case 5: return "pending";
      case 1: return "starting";
      case 2: return "running";
      case 3: return "idle";
      case 4: return "failed";
      default: return "unknown";
    }
  }
}
```

### ReadWorkerStateTool Implementation
```javascript
// w$ class - ReadWorkerStateTool (From Source Code)
w$ = class extends lr {
  constructor(r) {
    super(qn.readWorkerState, Nt.Safe);
    this._apiServer = r;
  }

  _logger = Te("ReadWorkerStateTool");

  description = `Read Worker Agent State (Instantaneous Status Check)

Get an INSTANTANEOUS snapshot of worker agent states for immediate status checking. This tool provides a quick status check without waiting - use WaitForWorkerAgentTool for monitoring and waiting instead of polling this tool repeatedly.

This tool:
- Returns immediate status snapshot (no waiting)
- Shows current state: unspecified, pending, starting, running, idle, failed
- Provides basic agent information and status
- Does NOT wait for status changes (use WaitForWorkerAgentTool for that)

WHEN TO USE:
- For quick status checks before other operations
- To verify worker agent existence and current state
- For debugging and troubleshooting worker issues

AVOID:
- Repeatedly polling this tool to wait for completion (use WaitForWorkerAgentTool instead)
- Using this for monitoring over time (inefficient compared to WaitForWorkerAgentTool)

Pass an array of worker agent IDs. If the array is empty, reads state for all available worker agents.`;

  async call(r, n, i, s) {
    let requestId = this._apiServer.createRequestId();

    try {
      let workerIds = r.worker_agent_ids;

      if (!Array.isArray(workerIds)) {
        return nt("Worker agent IDs must be an array.", requestId);
      }

      let remoteAgents = (await this._apiServer.listRemoteAgents()).remote_agents;
      let targetIds;

      if (workerIds.length === 0) {
        targetIds = remoteAgents.map(agent => agent.remote_agent_id);
        this._logger.debug(`Reading state for all ${targetIds.length} worker agents`);
      } else {
        targetIds = workerIds.filter(id => id && id.trim());
        this._logger.debug(`Reading state for ${targetIds.length} specified worker agents`);
      }

      if (targetIds.length === 0) {
        return Rt(JSON.stringify({
          stateResults: [],
          message: "No worker agents to read state for."
        }), requestId);
      }

      let results = [];

      for (let i = 0; i < targetIds.length; i++) {
        let agentId = targetIds[i];

        try {
          let agent = remoteAgents.find(a => a.remote_agent_id === agentId);

          if (!agent) {
            results.push({
              agentId: agentId,
              success: false,
              error: `Worker agent ${agentId} not found`
            });
            continue;
          }

          this._logger.debug(`Reading state for worker agent ${i + 1}/${targetIds.length}: ${agentId}`);

          results.push({
            agentId: agentId,
            success: true,
            status: this._getStatusString(agent.status),
            statusCode: agent.status,
            summary: agent.summary || "No summary available"
          });

        } catch (error) {
          this._logger.error(`Failed to read state for worker agent ${agentId}`, error);
          results.push({
            agentId: agentId,
            success: false,
            error: `Failed to read worker agent state: ${error instanceof Error ? error.message : String(error)}`
          });
        }
      }

      let finalResults = {
        stateResults: results.filter(r => r.success),
        failedReads: results.filter(r => !r.success),
        totalRequested: targetIds.length,
        totalSuccessful: results.filter(r => r.success).length,
        totalFailed: results.filter(r => !r.success).length
      };

      return Rt(JSON.stringify(finalResults), requestId);

    } catch (error) {
      this._logger.error("Failed to read worker agent states", error);
      return nt(`Failed to read worker agent states: ${error instanceof Error ? error.message : String(error)}`, requestId);
    }
  }

  _getStatusString(status) {
    switch (status) {
      case 0: return "unspecified";
      case 5: return "pending";
      case 1: return "starting";
      case 2: return "running";
      case 3: return "idle";
      case 4: return "failed";
      default: return "unknown";
    }
  }
}
```

### DeleteWorkerAgentTool Implementation
```javascript
// M$ class - DeleteWorkerAgentTool (From Source Code)
M$ = class extends lr {
  constructor(r) {
    super(qn.deleteWorkerAgent, Nt.Unsafe);
    this._apiServer = r;
  }

  _logger = Te("DeleteWorkerAgentTool");

  description = `Delete Worker Agents

Delete one or more worker agents and clean up their resources.

Pass an array of worker agent IDs. If the array is empty, deletes all available worker agents.

WARNING: Deleting agents before retrieving their work will permanently lose any changes they made. Use ReadWorkerAgentEditsTool first to collect completed work and manually pull it into your local workspace.`;

  async call(r, n, i, s) {
    let requestId = this._apiServer.createRequestId();

    try {
      let workerIds = r.worker_agent_ids;

      if (!Array.isArray(workerIds)) {
        return nt("Worker agent IDs must be an array.", requestId);
      }

      let remoteAgents = (await this._apiServer.listRemoteAgents()).remote_agents;
      let targetIds;

      if (workerIds.length === 0) {
        targetIds = remoteAgents.map(agent => agent.remote_agent_id);
        this._logger.debug(`Deleting all ${targetIds.length} worker agents`);
      } else {
        targetIds = workerIds.filter(id => id && id.trim());
        this._logger.debug(`Deleting ${targetIds.length} specified worker agents`);
      }

      if (targetIds.length === 0) {
        return Rt(JSON.stringify({
          deleteResults: [],
          message: "No worker agents to delete."
        }), requestId);
      }

      let results = [];

      for (let i = 0; i < targetIds.length; i++) {
        let agentId = targetIds[i];

        try {
          if (!remoteAgents.find(agent => agent.remote_agent_id === agentId)) {
            results.push({
              agentId: agentId,
              success: false,
              error: `Worker agent ${agentId} not found`
            });
            continue;
          }

          this._logger.debug(`Deleting worker agent ${i + 1}/${targetIds.length}: ${agentId}`);

          await this._apiServer.deleteRemoteAgent(agentId);

          this._logger.debug(`Successfully deleted worker agent: ${agentId}`);

          results.push({
            agentId: agentId,
            success: true
          });

        } catch (error) {
          this._logger.error(`Failed to delete worker agent ${agentId}`, error);
          results.push({
            agentId: agentId,
            success: false,
            error: `Failed to delete worker agent: ${error instanceof Error ? error.message : String(error)}`
          });
        }
      }

      let finalResults = {
        deleteResults: results.filter(r => r.success),
        failedDeletes: results.filter(r => !r.success),
        totalRequested: targetIds.length,
        totalSuccessful: results.filter(r => r.success).length,
        totalFailed: results.filter(r => !r.success).length
      };

      return Rt(JSON.stringify(finalResults), requestId);

    } catch (error) {
      this._logger.error("Failed to delete worker agents", error);
      return nt(`Failed to delete worker agents: ${error instanceof Error ? error.message : String(error)}`, requestId);
    }
  }
}
```

---

## 🔧 SUPPORTING CONSTANTS AND ENUMS

### Tool Names Enum (qn)
```javascript
const qn = {
  startWorkerAgent: "start_worker_agent",
  readWorkerState: "read_worker_state",
  waitForWorkerAgent: "wait_for_worker_agent",
  sendInstructionToWorkerAgent: "send_instruction_to_worker_agent",
  readWorkerAgentEdits: "read_worker_agent_edits",
  deleteWorkerAgent: "delete_worker_agent"
};
```

### Tool Safety Levels (Nt)
```javascript
const Nt = {
  Safe: "safe",      // Read-only operations
  Unsafe: "unsafe"   // Operations that modify state/resources
};
```

### File Change Types
```javascript
const FileChangeType = {
  ADDED: 0,
  DELETED: 1,
  MODIFIED: 2,
  RENAMED: 3
};
```

### Worker Agent Status Codes
```javascript
const WorkerStatus = {
  UNSPECIFIED: 0,  // Unknown/uninitialized state
  STARTING: 1,     // Agent is being created
  RUNNING: 2,      // Agent is actively working
  IDLE: 3,         // Agent completed work successfully
  FAILED: 4,       // Agent encountered an error
  PENDING: 5       // Agent is queued/waiting to start
};
```

### Message Types (ma)
```javascript
const ma = {
  TEXT: 0,           // Plain text messages/instructions
  TOOL_RESULT: 1,    // Results from tool execution
  IMAGE: 2,          // Image content
  IMAGE_ID: 3,       // Image identifier reference
  IDE_STATE: 4,      // IDE state information
  EDIT_EVENTS: 5,    // File edit event data
  CHECKPOINT_REF: 6, // Checkpoint reference
  CHANGE_PERSONALITY: 7, // Personality change instruction
  FILE: 8,           // File content
  FILE_ID: 9         // File identifier reference
};
```

---

## � COMPLETE WORKFLOW EXAMPLES

### Example 1: Single Task Delegation
```javascript
// 1. Start a worker for a specific task
const startResult = await startWorkerAgent({
  workers: [{
    summary: "Implement user authentication system",
    initial_prompt: "Create a complete JWT-based authentication system with user registration, login, logout, and password reset functionality. Include proper error handling and validation.",
    repository_url: "https://github.com/myorg/myproject.git",
    git_ref: "feature/auth-system"
  }]
});

const workerId = startResult.startedWorkers[0].workerAgentId;

// 2. Wait for completion
const waitResult = await waitForWorkerAgent({
  wait_operations: [{
    worker_agent_id: workerId,
    target_status: "idle",
    timeout_seconds: 1200
  }]
});

// 3. Retrieve the completed work
const editsResult = await readWorkerAgentEdits({
  worker_agent_ids: [workerId]
});

// 4. Clean up
await deleteWorkerAgent({
  worker_agent_ids: [workerId]
});
```

### Example 2: Multi-Worker Parallel Development
```javascript
// Start multiple workers for different components
const workers = await startWorkerAgent({
  workers: [
    {
      summary: "Frontend React components",
      initial_prompt: "Create React components for user dashboard with responsive design"
    },
    {
      summary: "Backend API endpoints",
      initial_prompt: "Implement REST API endpoints for user management with proper validation"
    },
    {
      summary: "Database schema and migrations",
      initial_prompt: "Design and implement database schema with proper indexing and constraints"
    }
  ]
});

const workerIds = workers.startedWorkers.map(w => w.workerAgentId);

// Monitor progress
const states = await readWorkerState({worker_agent_ids: workerIds});

// Wait for all to complete
const completions = await waitForWorkerAgent({
  wait_operations: workerIds.map(id => ({
    worker_agent_id: id,
    target_status: "idle",
    timeout_seconds: 1800
  }))
});

// Collect all results
const allEdits = await readWorkerAgentEdits({worker_agent_ids: workerIds});

// Clean up all workers
await deleteWorkerAgent({worker_agent_ids: workerIds});
```

### Example 3: Interactive Development with Dynamic Instructions
```javascript
// Start worker
const worker = await startWorkerAgent({
  workers: [{
    summary: "E-commerce checkout system",
    initial_prompt: "Implement a basic checkout flow with cart management"
  }]
});

const workerId = worker.startedWorkers[0].workerAgentId;

// Check progress periodically and send additional instructions
const state1 = await readWorkerState({worker_agent_ids: [workerId]});

if (state1.stateResults[0].status === "running") {
  // Send additional requirements
  await sendInstructionToWorkerAgent({
    instructions: [{
      worker_agent_id: workerId,
      instruction: "Also add payment processing integration with Stripe and comprehensive error handling for failed payments"
    }]
  });
}

// Later, add more requirements
await sendInstructionToWorkerAgent({
  instructions: [{
    worker_agent_id: workerId,
    instruction: "Include email notifications for order confirmation and shipping updates"
  }]
});

// Wait for final completion
await waitForWorkerAgent({
  wait_operations: [{
    worker_agent_id: workerId,
    target_status: "idle",
    timeout_seconds: 2400
  }]
});

// Retrieve final results
const finalEdits = await readWorkerAgentEdits({worker_agent_ids: [workerId]});
```

---

## �🏆 CONCLUSION

The Worker Agent System provides a complete distributed computing platform with:
- **Full Lifecycle Management** - Create, monitor, control, and cleanup workers
- **Real-time Communication** - Dynamic instruction sending and status monitoring
- **Result Integration** - Seamless retrieval of completed work
- **Robust Error Handling** - Comprehensive error reporting and recovery
- **Scalable Architecture** - Support for multiple concurrent workers
- **Interactive Control** - Dynamic instruction sending during execution
- **Efficient Monitoring** - Streaming-based status updates with polling fallback

This system enables true distributed development workflows where complex tasks can be delegated to specialized worker agents running in isolated environments, with full control over their lifecycle and seamless integration of their results back into the local development environment.
