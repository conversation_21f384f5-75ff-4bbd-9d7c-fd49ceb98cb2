
const fs = require('fs');
const zlib = require('zlib');
const path = require('path');

function compressJSFile(inputPath, outputPath = null, compressionLevel = 6) {
    try {
        // Read the JavaScript file
        const jsContent = fs.readFileSync(inputPath, 'utf8');
        
        // Compress using deflate with specified level
        const compressed = zlib.deflateSync(jsContent, { level: compressionLevel });
        
        // Generate output path if not provided
        if (!outputPath) {
            const parsedPath = path.parse(inputPath);
            outputPath = path.join(parsedPath.dir, parsedPath.name + '.deflate');
        }
        
        // Write compressed data to file
        fs.writeFileSync(outputPath, compressed);
        
        const originalSize = Buffer.byteLength(jsContent, 'utf8');
        const compressedSize = compressed.length;
        const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);
        
        console.log(`Compression complete (level ${compressionLevel}):`);
        console.log(`Original size: ${originalSize} bytes`);
        console.log(`Compressed size: ${compressedSize} bytes`);
        console.log(`Compression ratio: ${compressionRatio}%`);
        console.log(`Output file: ${outputPath}`);
        
        return compressed;
        
    } catch (error) {
        console.error('Error compressing file:', error.message);
        process.exit(1);
    }
}

// Command line usage
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('Usage: node compress.js <input-file.js> [output-file.deflate] [compression-level]');
        console.log('Compression levels: 0=no compression, 1=fastest, 6=default, 9=best compression');
        console.log('Example: node compress.js script.js');
        console.log('Example: node compress.js script.js compressed.deflate 9');
        process.exit(1);
    }
    
    const inputFile = args[0];
    const outputFile = args[1] || null;
    const compressionLevel = args[2] ? parseInt(args[2]) : 6;
    
    if (compressionLevel < 0 || compressionLevel > 9) {
        console.error('Error: Compression level must be between 0-9');
        process.exit(1);
    }
    
    if (!fs.existsSync(inputFile)) {
        console.error(`Error: File '${inputFile}' not found`);
        process.exit(1);
    }
    
    compressJSFile(inputFile, outputFile, compressionLevel);
}

// Export for use as module
module.exports = { compressJSFile };