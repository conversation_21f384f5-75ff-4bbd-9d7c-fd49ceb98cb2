# 🔥 WEB-SEARCH - <PERSON>LT<PERSON><PERSON><PERSON> COMPLETE TOOL DOCUMENTATION 🔥

## 🎯 TOOL OVERVIEW
- **Tool Name**: web-search
- **Tool ID**: 17
- **Category**: Web and External Tools - Web Search Capabilities and Integration
- **Extraction Date**: 2025-07-16T20:50:00.000Z
- **System Class**: External Web Integration System
- **Logger**: Integrated with web search system
- **Safety Classification**: `Nt.Safe` - Highest safety tier (read-only)

## 🚀 BREAKTHROUGH DISCOVERY: ADVANCED WEB SEARCH INTEGRATION

**MAJOR FINDING**: This tool provides comprehensive web search capabilities:
1. **Google Custom Search API** - Uses Google's Custom Search API for results
2. **Markdown Result Formatting** - Returns results in structured markdown format
3. **Configurable Result Count** - Adjustable number of search results
4. **Rich Result Information** - URL, title, and page snippets included
5. **Real-time Web Access** - Live web search capabilities

---

## 📋 COMPLETE TOOL DESCRIPTION FROM SOURCE CODE

**Full Description Extracted:**

```
Search the web for information. Returns results in markdown format.
Each result includes the URL, title, and a snippet from the page if available.

This tool uses Google's Custom Search API to find relevant web pages.
```

**Critical Capabilities:**
- ✅ **Web search functionality** - Search the web for information
- ✅ **Google Custom Search API** - Uses Google's advanced search technology
- ✅ **Markdown formatting** - Structured result presentation
- ✅ **Rich result data** - URL, title, and page snippets
- ✅ **Configurable results** - Adjustable number of search results

## 🔧 COMPLETE TOOL SCHEMA FROM SOURCE CODE

**Complete JSON Schema Extracted:**

```json
{
  "type": "object",
  "properties": {
    "query": {
      "type": "string",
      "description": "The search query to send.",
      "title": "Query"
    },
    "num_results": {
      "type": "integer",
      "description": "Number of results to return",
      "default": 5,
      "minimum": 1,
      "maximum": 10,
      "title": "Num Results"
    }
  },
  "required": ["query"],
  "title": "WebSearchInput"
}
```

## 💡 USAGE EXAMPLES (INCLUDING SOURCE CODE CONSTRAINTS)

### 🎯 BASIC SEARCH EXAMPLES

**Default Search (Source Code Default: 5 results):**
```json
{
  "query": "Python machine learning tutorials"
}
```

**Custom Result Count (Source Code Constraints: 1-10):**
```json
{
  "query": "React TypeScript best practices",
  "num_results": 3
}
```

**Maximum Results (Source Code Maximum: 10):**
```json
{
  "query": "JavaScript performance optimization",
  "num_results": 10
}
```

**Minimum Results (Source Code Minimum: 1):**
```json
{
  "query": "Docker container security",
  "num_results": 1
}
```

### 🎯 ADVANCED SEARCH EXAMPLES

**Technical Documentation Search:**
```json
{
  "query": "PostgreSQL indexing performance tuning",
  "num_results": 7
}
```

**Framework Comparison Search:**
```json
{
  "query": "Vue.js vs React vs Angular 2024 comparison",
  "num_results": 5
}
```

## 🚨 CRITICAL FEATURES

### 🛡️ SEARCH CAPABILITIES
- **Google Custom Search API**: Advanced search technology integration
- **Markdown Formatting**: Structured result presentation
- **Rich Result Data**: URL, title, and page snippets included
- **Configurable Results**: 1-10 results per search
- **Real-time Access**: Live web search capabilities

### ⚡ PERFORMANCE OPTIMIZATION
- **API Integration**: Efficient Google Custom Search API usage
- **Result Limiting**: Configurable result count for performance
- **Structured Output**: Organized markdown result formatting
- **Error Handling**: Robust error handling for search failures
- **Rate Limiting**: Appropriate API usage management

## 🎯 BEST PRACTICES

### ✅ RECOMMENDED USAGE
1. **Use specific queries** - More specific searches yield better results
2. **Adjust result count** - Use appropriate number of results for context
3. **Include relevant keywords** - Add technology/framework names for better targeting
4. **Use for research** - Perfect for technical research and documentation
5. **Combine with web-fetch** - Use together for comprehensive web analysis

### ❌ AVOID THESE PATTERNS
1. **Don't use overly broad queries** - Be specific for better results
2. **Avoid excessive result counts** - More results aren't always better
3. **Don't ignore result quality** - Focus on relevant, high-quality results
4. **Avoid repetitive searches** - Refine queries instead of repeating
5. **Don't rely solely on snippets** - Use web-fetch for complete content

## 📊 PERFORMANCE CHARACTERISTICS

### ⚡ OPTIMIZATION FEATURES
- **Google Custom Search API**: High-quality search results
- **Configurable Result Count**: Optimized result quantity control
- **Markdown Formatting**: Structured result presentation
- **Error Recovery**: Robust error handling for search failures
- **Rate Management**: Appropriate API usage and rate limiting

## 🔗 RELATED TOOLS

### 🤝 COMPLEMENTARY TOOLS
- **web-fetch**: Fetch complete content from search results
- **open-browser**: Open search results in browser
- **codebase-retrieval**: Internal code search
- **memory-retrieval**: Search stored memories

---

## 🏆 CONCLUSION

The **web-search** tool provides essential web search capabilities for development workflows. With its Google Custom Search API integration and structured markdown results, it enables comprehensive web research and information retrieval.

**Key Strengths:**
- ✅ Google Custom Search API integration
- ✅ Structured markdown result formatting
- ✅ Configurable result count (1-10)
- ✅ Rich result information (URL, title, snippets)
- ✅ Real-time web search capabilities
- ✅ Robust error handling

This tool is **ESSENTIAL** for web research, technical documentation discovery, troubleshooting assistance, and staying current with development trends and best practices.

---

*Generated by Ultimate Tool Extractor - Complete web-search Tool Documentation*
